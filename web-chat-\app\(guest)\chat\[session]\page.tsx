'use client';

import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useChat } from '../../../hooks/useChat';
import { ChatInterface } from '../../../components/chat/ChatInterface';
import { LanguageSelector } from '../../../components/language/LanguageSelector';
import { TranslationToggle } from '../../../components/language/TranslationToggle';

// Force dynamic rendering to avoid static generation issues
export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default function ChatPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  // URL parameters
  const sessionId = params.session as string;
  const tempUserId = searchParams.get('temp_user');
  const language = searchParams.get('lang') || 'en';
  const qrCode = searchParams.get('qr');
  const sessionToken = searchParams.get('token');

  // State management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<any>(null);
  const [currentLanguage, setCurrentLanguage] = useState(language);
  const [autoTranslateEnabled, setAutoTranslateEnabled] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  // Chat hook with realtime support - only initialize when we have valid session data
  const {
    messages,
    session,
    loading: chatLoading,
    connected,
    sendMessage,
    isTyping,
    startTyping,
    stopTyping,
    setAutoTranslate,
    error: chatError,
    realtimeConnected,
    usePollingFallback
  } = useChat({
    sessionId: sessionData?.id || '',
    guestId: tempUserId || '',
    autoTranslate: autoTranslateEnabled,
    guestLanguage: currentLanguage
  });

  // Initialize session
  useEffect(() => {
    if (sessionId) {
      initializeChatSession();
    }
  }, [sessionId]);

  const initializeChatSession = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Initializing chat session:', {
        sessionId,
        tempUserId,
        language,
        qrCode,
        sessionToken
      });

      if (sessionId === 'new') {
        // Check for existing sessions first
        if (qrCode && tempUserId) {
          console.log('🔍 Checking for existing sessions...');

          const checkResponse = await fetch('/api/chat-sessions/check', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              qr_code_value: qrCode,
              device_id: generateDeviceFingerprint(),
              temp_user_id: tempUserId,
              check_type: 'room_device'
            }),
          });

          if (checkResponse.ok) {
            const checkData = await checkResponse.json();

            if (checkData.has_existing_session && checkData.existing_session) {
              console.log('♻️ Found existing session, redirecting...');
              const existingSessionId = checkData.existing_session.id;
              const newUrl = `/chat/${existingSessionId}?temp_user=${tempUserId}&lang=${language}&qr=${qrCode}&token=${sessionToken}`;
              window.history.replaceState({}, '', newUrl);

              // Load the existing session
              const sessionResponse = await fetch(`/api/chat-sessions/${existingSessionId}`);
              if (sessionResponse.ok) {
                const sessionData = await sessionResponse.json();
                if (sessionData.success) {
                  setSessionData(sessionData.session);
                  return;
                }
              }
            }
          }
        }

        // Create new chat session
        console.log('🆕 Creating new chat session...');
        const response = await fetch('/api/chat-sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            temporary_user_id: tempUserId,
            guest_language: language,
            source_type: 'qr_code',
            source_qr_code_id: qrCode,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.session) {
            setSessionData(data.session);
            // Update URL with actual session ID
            const newUrl = `/chat/${data.session.id}?temp_user=${tempUserId}&lang=${language}&qr=${qrCode}&token=${sessionToken}`;
            window.history.replaceState({}, '', newUrl);
          } else {
            throw new Error(data.error || 'Failed to create session');
          }
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: Failed to create chat session`);
        }
      } else {
        // Load existing session
        console.log('📖 Loading existing session:', sessionId);
        const response = await fetch(`/api/chat-sessions/${sessionId}`);

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.session) {
            setSessionData(data.session);
          } else {
            throw new Error(data.error || 'Session not found');
          }
        } else {
          throw new Error(`Failed to load session: ${response.status}`);
        }
      }
    } catch (err) {
      console.error('❌ Error initializing chat session:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize chat session');
    } finally {
      setLoading(false);
    }
  };

  // Generate device fingerprint for session management
  const generateDeviceFingerprint = (): string => {
    if (typeof window === 'undefined') return 'server-side';

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('Device fingerprint', 2, 2);

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');

    return btoa(fingerprint).substring(0, 32);
  };

  // Handle language change
  const handleLanguageChange = (newLanguage: string) => {
    setCurrentLanguage(newLanguage);
    // Update URL parameter
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('lang', newLanguage);
    window.history.replaceState({}, '', newUrl.toString());
  };

  // Handle auto-translate toggle
  const handleAutoTranslateToggle = (enabled: boolean) => {
    setAutoTranslateEnabled(enabled);
    setAutoTranslate(enabled);
  };

  // Handle end chat
  const handleEndChat = () => {
    if (confirm('Are you sure you want to end this chat session?')) {
      router.push('/?chat=ended');
    }
  };

  // Get display name from session data
  const getDisplayName = () => {
    if (sessionData?.qr_info) {
      if (sessionData.qr_info.room_number) {
        return `Room ${sessionData.qr_info.room_number}`;
      }
      if (sessionData.qr_info.location) {
        return sessionData.qr_info.location;
      }
    }

    // Fallback for demo or unknown
    if (qrCode === 'DEMO-QR-001') {
      return 'Room 101 Guest';
    }

    return 'Guest User';
  };

  // Get location info for display
  const getLocationInfo = () => {
    if (sessionData?.qr_info) {
      const { room_number, location, description } = sessionData.qr_info;

      if (room_number) {
        return {
          type: 'room',
          display: `Room ${room_number}`,
          subtitle: location || description || 'Hotel Room'
        };
      }

      if (location) {
        return {
          type: 'area',
          display: location,
          subtitle: description || 'Hotel Area'
        };
      }
    }

    return {
      type: 'unknown',
      display: 'Guest Area',
      subtitle: 'Hotel Service'
    };
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-700">Connecting to Chat...</h3>
          <p className="text-gray-500">Please wait while we set up your chat session</p>
          {sessionId === 'new' && (
            <p className="text-sm text-gray-400 mt-2">Creating new session...</p>
          )}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-600 mb-2">Connection Failed</h3>
          <p className="text-gray-600 mb-2 text-sm">{error}</p>
          <details className="text-xs text-gray-400 mb-6">
            <summary className="cursor-pointer">Debug Info</summary>
            <div className="mt-2 text-left bg-gray-50 p-2 rounded">
              <div>Session ID: {sessionId}</div>
              <div>Temp User: {tempUserId}</div>
              <div>Language: {language}</div>
              <div>QR Code: {qrCode}</div>
            </div>
          </details>
          <div className="flex gap-3">
            <button
              onClick={initializeChatSession}
              className="flex-1 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={() => router.push('/')}
              className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Main chat interface
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Enhanced Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 shadow-sm">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <button
            onClick={() => router.push('/')}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back
          </button>

          <div className="flex items-center space-x-4">
            {/* Connection Status */}
            <div className="flex items-center space-x-2 text-sm">
              <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <span className="text-gray-600">
                {connected ? 'Connected' : 'Disconnected'}
              </span>
            </div>

            {/* Location Info */}
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                getLocationInfo().type === 'room'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {getLocationInfo().type === 'room' ? '🏨' : '🏢'} {getDisplayName()}
              </span>

              {/* Session Status */}
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                sessionData?.status === 'active'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {sessionData?.status || 'connecting'}
              </span>
            </div>

            {/* Session ID */}
            <span className="bg-gray-100 px-2 py-1 rounded font-mono text-xs">
              {sessionData?.id?.substring(0, 8) || 'Loading'}...
            </span>

            {/* Settings Toggle */}
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="text-gray-600 hover:text-gray-800 transition-colors p-1 rounded hover:bg-gray-100"
              title="Settings"
            >
              ⚙️
            </button>
          </div>

          <button
            onClick={handleEndChat}
            className="text-red-600 hover:text-red-800 transition-colors text-sm font-medium px-3 py-1 rounded hover:bg-red-50"
          >
            End Chat
          </button>
        </div>

        {/* Location Subtitle */}
        {sessionData?.qr_info && (
          <div className="max-w-4xl mx-auto mt-2">
            <p className="text-xs text-gray-500 text-center">
              📍 {getLocationInfo().subtitle}
              {sessionData.qr_info.description && sessionData.qr_info.description !== getLocationInfo().subtitle &&
                ` • ${sessionData.qr_info.description}`
              }
            </p>
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-blue-50 border-b border-gray-200 px-4 py-3">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                {/* Language Selector */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Language:</span>
                  <LanguageSelector
                    currentLanguage={currentLanguage}
                    onLanguageChange={handleLanguageChange}
                    compact={true}
                  />
                </div>

                {/* Translation Toggle */}
                <TranslationToggle
                  enabled={autoTranslateEnabled}
                  onToggle={handleAutoTranslateToggle}
                  guestLanguage={currentLanguage}
                  staffLanguage="en"
                  showLanguages={true}
                />
              </div>

              <button
                onClick={() => setShowSettings(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Chat Interface */}
      <div className="flex-1 flex">
        <div className="flex-1 max-w-4xl mx-auto">
          {sessionData && (
            <ChatInterface
              sessionId={sessionData.id}
              guestId={tempUserId || ''}
              guestLanguage={currentLanguage}
              messages={messages}
              session={session}
              loading={chatLoading}
              connected={connected}
              onSendMessage={sendMessage}
              onStartTyping={startTyping}
              onStopTyping={stopTyping}
              isTyping={isTyping}
              error={chatError}
              realtimeConnected={realtimeConnected}
              usePollingFallback={usePollingFallback}
            />
          )}
        </div>
      </div>

      {/* Enhanced Footer */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="max-w-4xl mx-auto text-center text-sm text-gray-500">
          🔒 Secure Connection •
          {realtimeConnected ? ' ⚡ Real-time Messaging' : usePollingFallback ? ' 🔄 Polling Mode' : ' 🔌 Connecting'} •
          🌐 Auto Translation •
          🏨 LoaLoa Hotel
        </div>
      </div>
    </div>
  );
}
