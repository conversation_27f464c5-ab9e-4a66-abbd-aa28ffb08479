// Enhanced CSS Variables for theming - Using :global() for CSS Modules compatibility
:global(:root) {
  --chat-primary: #f97316;
  --chat-primary-dark: #ea580c;
  --chat-primary-light: #fed7aa;
  --chat-secondary: #6b7280;
  --chat-background: #ffffff;
  --chat-surface: #f8fafc;
  --chat-surface-hover: #f1f5f9;
  --chat-border: #e5e7eb;
  --chat-border-light: #f3f4f6;
  --chat-text: #111827;
  --chat-text-light: #6b7280;
  --chat-text-muted: #9ca3af;
  --chat-success: #10b981;
  --chat-success-light: #dcfce7;
  --chat-error: #ef4444;
  --chat-error-light: #fef2f2;
  --chat-warning: #f59e0b;
  --chat-warning-light: #fef3c7;
  --chat-info: #3b82f6;
  --chat-info-light: #dbeafe;
  --chat-radius: 0.75rem;
  --chat-radius-sm: 0.5rem;
  --chat-radius-lg: 1rem;
  --chat-radius-xl: 1.5rem;
  --chat-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --chat-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --chat-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --chat-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  --chat-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --chat-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --chat-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

// Enhanced Chat Interface
.chatInterface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  background: linear-gradient(135deg, var(--chat-surface) 0%, var(--chat-background) 100%);
  border-radius: var(--chat-radius-lg);
  overflow: hidden;
  box-shadow: var(--chat-shadow-xl);
  position: relative;
  
  // Glass morphism effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
  }
}

// Enhanced Error Banner
.errorBanner {
  background: var(--chat-error-light);
  border-left: 4px solid var(--chat-error);
  padding: 1rem 1.5rem;
  animation: slideDown 0.4s var(--chat-spring);
  position: relative;
  
  &::before {
    content: '⚠️';
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.25rem;
    animation: pulse 2s infinite;
  }
  
  .errorContent {
    margin-left: 2rem;
    color: var(--chat-error);
    font-weight: 500;
  }
}

// Enhanced Messages Area
.messagesArea {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: linear-gradient(to bottom, 
    rgba(248, 250, 252, 0.8) 0%, 
    rgba(255, 255, 255, 0.9) 50%,
    rgba(248, 250, 252, 0.8) 100%
  );
  scroll-behavior: smooth;
  position: relative;
  
  // Enhanced scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.5);
    border-radius: 4px;
    margin: 0.5rem 0;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--chat-primary-light), var(--chat-primary));
    border-radius: 4px;
    transition: var(--chat-transition);
    
    &:hover {
      background: linear-gradient(180deg, var(--chat-primary), var(--chat-primary-dark));
    }
  }
  
  // Scroll fade effect
  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 20px;
    pointer-events: none;
    z-index: 2;
  }
  
  &::before {
    top: 0;
    background: linear-gradient(to bottom, var(--chat-surface), transparent);
  }
  
  &::after {
    bottom: 0;
    background: linear-gradient(to top, var(--chat-surface), transparent);
  }
}

// Enhanced Welcome Message
.welcomeMessage {
  text-align: center;
  padding: 3rem 1rem;
  animation: fadeInUp 0.8s var(--chat-spring);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, var(--chat-primary-light) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0.3;
    z-index: -1;
    animation: pulse 3s infinite;
  }
  
  .icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(249, 115, 22, 0.3));
  }
  
  .title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--chat-text);
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, var(--chat-primary), var(--chat-primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .subtitle {
    color: var(--chat-text-light);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
  }
  
  .badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
  }
  
  .badge {
    padding: 0.5rem 1rem;
    border-radius: var(--chat-radius-xl);
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--chat-transition);
    cursor: default;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--chat-shadow-md);
    }
    
    &.connected {
      background: linear-gradient(135deg, var(--chat-success-light), rgba(16, 185, 129, 0.1));
      color: var(--chat-success);
      border-color: var(--chat-success);
    }
    
    &.language {
      background: linear-gradient(135deg, var(--chat-info-light), rgba(59, 130, 246, 0.1));
      color: var(--chat-info);
      border-color: var(--chat-info);
    }
    
    &.translation {
      background: linear-gradient(135deg, var(--chat-warning-light), rgba(245, 158, 11, 0.1));
      color: var(--chat-warning);
      border-color: var(--chat-warning);
    }
  }
}

// Enhanced Messages List
.messagesList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem 0;
  position: relative;
  z-index: 1;
}

// Enhanced Message Container
.messageContainer {
  display: flex;
  animation: messageSlideIn 0.4s var(--chat-spring);
  position: relative;
  
  &.guest {
    justify-content: flex-end;
    
    .messageBubble {
      animation-delay: 0.1s;
    }
  }
  
  &.staff {
    justify-content: flex-start;
    
    .messageBubble {
      animation-delay: 0.05s;
    }
  }
  
  // Message hover effect
  &:hover {
    .messageBubble {
      transform: translateY(-1px);
      box-shadow: var(--chat-shadow-md);
    }
    
    .messageTime {
      opacity: 1;
    }
  }
}

// Enhanced Message Bubble
.messageBubble {
  max-width: 22rem;
  padding: 1rem 1.25rem;
  border-radius: var(--chat-radius-lg);
  position: relative;
  word-wrap: break-word;
  transition: var(--chat-transition);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  @media (min-width: 1024px) {
    max-width: 28rem;
  }
  
  &.guest {
    background: linear-gradient(135deg, var(--chat-primary) 0%, var(--chat-primary-dark) 100%);
    color: white;
    border-bottom-right-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
    
    // Enhanced tail
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: var(--chat-primary-dark);
      border-left-color: var(--chat-primary-dark);
      filter: drop-shadow(2px 2px 4px rgba(249, 115, 22, 0.2));
    }
  }
  
  &.staff {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    color: var(--chat-text);
    border-bottom-left-radius: 0.375rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--chat-border-light);
    
    // Enhanced tail
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: rgba(248, 250, 252, 0.9);
      border-right-color: rgba(248, 250, 252, 0.9);
      filter: drop-shadow(-2px 2px 4px rgba(0, 0, 0, 0.05));
    }
  }
}

// Enhanced Message Header
.messageHeader {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  opacity: 0.8;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .senderName {
    font-weight: 600;
  }

  .messageTime {
    opacity: 0.6;
    transition: var(--chat-transition);
  }
}

// Enhanced Message Content
.messageContent {
  font-size: 0.9rem;
  line-height: 1.5;
  position: relative;

  // Text selection styling
  &::selection {
    background: rgba(255, 255, 255, 0.3);
  }

  // Link styling within messages
  a {
    color: inherit;
    text-decoration: underline;
    text-decoration-color: rgba(255, 255, 255, 0.5);
    transition: var(--chat-transition);

    &:hover {
      text-decoration-color: currentColor;
    }
  }
}

// Enhanced Original Content (for translations)
.originalContent {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  font-style: italic;
  opacity: 0.8;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--chat-radius-sm);
  border-left: 3px solid rgba(255, 255, 255, 0.3);

  &::before {
    content: '📝 ';
    opacity: 0.7;
  }
}

// Enhanced Translation Info
.translationInfo {
  font-size: 0.75rem;
  margin-top: 0.5rem;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.5rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--chat-radius-sm);

  .translationIcon {
    animation: rotate 2s linear infinite;
  }
}

// Enhanced Typing Indicator
.typingIndicator {
  display: flex;
  justify-content: flex-start;
  animation: messageSlideIn 0.3s var(--chat-spring);

  .bubble {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    color: var(--chat-text);
    max-width: 20rem;
    padding: 1rem 1.25rem;
    border-radius: var(--chat-radius-lg);
    border-bottom-left-radius: 0.375rem;
    box-shadow: var(--chat-shadow);
    border: 1px solid var(--chat-border-light);
    backdrop-filter: blur(10px);
    position: relative;

    // Tail for typing indicator
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -10px;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-top-color: rgba(248, 250, 252, 0.95);
      border-right-color: rgba(248, 250, 252, 0.95);
    }

    .dots {
      display: flex;
      align-items: center;
      gap: 0.375rem;

      .dot {
        width: 0.5rem;
        height: 0.5rem;
        background: var(--chat-primary);
        border-radius: 50%;

        &:nth-child(1) {
          animation: typingDot 1.4s infinite;
        }

        &:nth-child(2) {
          animation: typingDot 1.4s infinite 0.2s;
        }

        &:nth-child(3) {
          animation: typingDot 1.4s infinite 0.4s;
        }
      }
    }

    .text {
      font-size: 0.8rem;
      color: var(--chat-text-light);
      margin-left: 0.75rem;
      font-weight: 500;
    }
  }
}

// Enhanced Input Area
.inputArea {
  border-top: 1px solid var(--chat-border);
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  position: relative;

  // Glass effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }
}

// Enhanced Input Container
.inputContainer {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  position: relative;
}

// Enhanced Message Input
.messageInput {
  flex: 1;
  border: 2px solid var(--chat-border);
  border-radius: var(--chat-radius-lg);
  padding: 1rem 1.25rem;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: none;
  transition: var(--chat-transition);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  min-height: 2.5rem;
  max-height: 8rem;

  &:focus {
    outline: none;
    border-color: var(--chat-primary);
    box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
  }

  &:disabled {
    background: rgba(249, 250, 251, 0.8);
    cursor: not-allowed;
    opacity: 0.6;
  }

  &::placeholder {
    color: var(--chat-text-muted);
    font-style: italic;
  }
}

// Enhanced Send Button
.sendButton {
  background: linear-gradient(135deg, var(--chat-primary) 0%, var(--chat-primary-dark) 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--chat-radius-lg);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
  transition: var(--chat-transition);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  position: relative;
  overflow: hidden;

  // Ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: width 0.3s, height 0.3s, top 0.3s, left 0.3s;
    transform: translate(-50%, -50%);
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--chat-primary-dark) 0%, #dc2626 100%);
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    background: var(--chat-text-muted);
  }

  .spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .text {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  .icon {
    font-size: 1.1rem;
    transition: var(--chat-transition);
  }

  &:hover .icon {
    transform: translateX(2px);
  }
}

// Enhanced Status Bar
.statusBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  font-size: 0.75rem;
  color: var(--chat-text-light);
  padding: 0.75rem 1rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: var(--chat-radius);
  backdrop-filter: blur(10px);
}

.statusLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connectionStatus {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-weight: 500;

  .indicator {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    position: relative;

    &.connected {
      background: var(--chat-success);

      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: var(--chat-success);
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    &.disconnected {
      background: var(--chat-error);
      animation: blink 1s infinite;
    }
  }

  &.connected {
    color: var(--chat-success);
  }

  &.disconnected {
    color: var(--chat-error);
  }
}

.syncStatus {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--chat-info);
  font-weight: 500;

  &::before {
    content: '🔄';
    animation: rotate 2s linear infinite;
  }
}

// Enhanced Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes typingDot {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

// Enhanced Responsive Design
@media (max-width: 768px) {
  .chatInterface {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .messagesArea {
    padding: 1rem;

    &::before,
    &::after {
      height: 15px;
    }
  }

  .welcomeMessage {
    padding: 2rem 1rem;

    .icon {
      font-size: 3rem;
    }

    .title {
      font-size: 1.25rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .badges {
      gap: 0.75rem;
    }

    .badge {
      padding: 0.375rem 0.75rem;
      font-size: 0.8rem;
    }
  }

  .messageBubble {
    max-width: 18rem;
    padding: 0.875rem 1rem;

    &.guest,
    &.staff {
      &::after {
        border-width: 8px;
      }
    }
  }

  .inputArea {
    padding: 1rem;
  }

  .inputContainer {
    gap: 0.75rem;
  }

  .messageInput {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }

  .sendButton {
    padding: 0.875rem 1rem;

    .text {
      display: none;
    }

    .icon {
      font-size: 1.25rem;
    }
  }

  .statusBar {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    padding: 0.5rem 0.75rem;
  }

  .statusLeft {
    gap: 0.75rem;
  }
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
  :global(:root) {
    --chat-background: #1f2937;
    --chat-surface: #374151;
    --chat-surface-hover: #4b5563;
    --chat-border: #4b5563;
    --chat-border-light: #6b7280;
    --chat-text: #f9fafb;
    --chat-text-light: #d1d5db;
    --chat-text-muted: #9ca3af;
  }

  .messageBubble.staff {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.9) 0%, rgba(75, 85, 99, 0.9) 100%);
    color: var(--chat-text);
    border-color: var(--chat-border);

    &::after {
      border-top-color: rgba(75, 85, 99, 0.9);
      border-right-color: rgba(75, 85, 99, 0.9);
    }
  }

  .typingIndicator .bubble {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.95) 0%, rgba(75, 85, 99, 0.95) 100%);
    color: var(--chat-text);
    border-color: var(--chat-border);

    &::after {
      border-top-color: rgba(75, 85, 99, 0.95);
      border-right-color: rgba(75, 85, 99, 0.95);
    }
  }

  .messageInput {
    background: rgba(55, 65, 81, 0.8);
    color: var(--chat-text);
    border-color: var(--chat-border);

    &:focus {
      background: rgba(55, 65, 81, 0.95);
    }

    &::placeholder {
      color: var(--chat-text-muted);
    }
  }
}

// Accessibility improvements
.chatInterface {
  // Focus management
  &:focus-within {
    .messageInput {
      border-color: var(--chat-primary);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .messageBubble {
    border-width: 2px;

    &.guest {
      border-color: var(--chat-primary-dark);
    }

    &.staff {
      border-color: var(--chat-text);
    }
  }

  .sendButton {
    border: 2px solid var(--chat-primary-dark);
  }

  .messageInput {
    border-width: 2px;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  :global(*) {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .welcomeMessage .icon {
    animation: none;
  }

  .typingIndicator .dot {
    animation: none;
    opacity: 0.7;
  }

  .syncStatus::before {
    animation: none;
  }
}

// Print styles
@media print {
  .chatInterface {
    box-shadow: none;
    border: 1px solid #000;
  }

  .inputArea {
    display: none;
  }

  .statusBar {
    display: none;
  }

  .messageBubble {
    box-shadow: none;
    border: 1px solid #ccc;

    &::after {
      display: none;
    }
  }

  .welcomeMessage {
    page-break-after: avoid;
  }
}
