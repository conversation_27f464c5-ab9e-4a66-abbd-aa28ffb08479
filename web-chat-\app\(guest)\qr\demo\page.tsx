'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function QRDemoPage() {
  const router = useRouter()
  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const [loading, setLoading] = useState(false)

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: 'ko', name: '한국어', flag: '🇰🇷' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'zh', name: '中文', flag: '🇨🇳' },
  ]

  const handleStartChat = async () => {
    setLoading(true)
    try {
      // Simulate QR scan with demo code
      const response = await fetch(`/api/qr-scan/DEMO-QR-001?lang=${selectedLanguage}&device=demo-device`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.redirect_url) {
          router.push(data.redirect_url)
        } else {
          alert('Failed to start chat: ' + (data.error || 'Unknown error'))
        }
      } else {
        alert('Failed to start chat. Please try again.')
      }
    } catch (error) {
      console.error('Error starting chat:', error)
      alert('Network error. Please check your connection.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">📱</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                QR Code Demo
              </h1>
              <p className="text-gray-600">
                This simulates scanning a QR code in a hotel room or area
              </p>
            </div>

            {/* Language Selection */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Your Language:
              </label>
              <div className="space-y-2">
                {languages.map((lang) => (
                  <label key={lang.code} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="language"
                      value={lang.code}
                      checked={selectedLanguage === lang.code}
                      onChange={(e) => setSelectedLanguage(e.target.value)}
                      className="mr-3"
                    />
                    <span className="text-2xl mr-3">{lang.flag}</span>
                    <span className="font-medium">{lang.name}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Start Chat Button */}
            <button
              onClick={handleStartChat}
              disabled={loading}
              className="w-full bg-orange-500 text-white py-4 px-6 rounded-lg font-semibold hover:bg-orange-600 transition-colors disabled:opacity-50"
            >
              {loading ? 'Starting Chat...' : '🚀 Start Chat Session'}
            </button>

            {/* Demo Info */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-800">
                <strong>Demo Info:</strong><br />
                • Location: Demo Hotel Room 101<br />
                • QR Code: DEMO-QR-001<br />
                • Auto-translation enabled
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
