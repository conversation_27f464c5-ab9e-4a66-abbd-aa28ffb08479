'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { LanguageSelector } from '../../components/language/LanguageSelector'
import styles from './ScanPage.module.scss'

export default function ScanPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedLanguage, setSelectedLanguage] = useState('en')
  const [qrInput, setQrInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get language from URL if provided
  useEffect(() => {
    const langParam = searchParams?.get('lang')
    if (langParam) {
      setSelectedLanguage(langParam)
    }
  }, [searchParams])

  const handleQRSubmit = async (qrCode: string) => {
    if (!qrCode.trim()) {
      setError('Please enter a QR code')
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('🔍 Processing QR code:', qrCode)

      // Navigate to QR scan processing page
      const scanUrl = `/qr/${encodeURIComponent(qrCode.trim())}?lang=${selectedLanguage}`
      router.push(scanUrl)

    } catch (err) {
      console.error('Error processing QR code:', err)
      setError('Failed to process QR code. Please try again.')
      setLoading(false)
    }
  }

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleQRSubmit(qrInput)
  }

  const handleDemoClick = () => {
    handleQRSubmit('DEMO-QR-001')
  }

  const handleQuickScan = (qrCode: string) => {
    handleQRSubmit(qrCode)
  }

  return (
    <div className={styles.container}>
      <div className={styles.scanCard}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.logo}>
            <span className={styles.logoIcon}>🏨</span>
            <h1 className={styles.logoText}>LoaLoa Hotel</h1>
          </div>
          <p className={styles.subtitle}>Scan QR Code to Start Chat</p>
        </div>

        {/* Language Selector */}
        <div className={styles.languageSection}>
          <label className={styles.languageLabel}>
            Choose your language:
          </label>
          <LanguageSelector
            currentLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
            compact={false}
          />
        </div>

        {/* QR Input Form */}
        <form onSubmit={handleFormSubmit} className={styles.qrForm}>
          <div className={styles.inputGroup}>
            <label htmlFor="qr-input" className={styles.inputLabel}>
              Enter QR Code:
            </label>
            <input
              id="qr-input"
              type="text"
              value={qrInput}
              onChange={(e) => setQrInput(e.target.value)}
              placeholder="Scan or type QR code here..."
              className={styles.qrInput}
              disabled={loading}
            />
          </div>

          {error && (
            <div className={styles.errorMessage}>
              <span className={styles.errorIcon}>⚠️</span>
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading || !qrInput.trim()}
            className={styles.submitButton}
          >
            {loading ? (
              <>
                <span className={styles.spinner}></span>
                Processing...
              </>
            ) : (
              <>
                <span className={styles.buttonIcon}>📱</span>
                Start Chat
              </>
            )}
          </button>
        </form>

        {/* Quick Actions */}
        <div className={styles.quickActions}>
          <h3 className={styles.quickTitle}>Quick Access:</h3>
          
          <div className={styles.actionButtons}>
            <button
              onClick={handleDemoClick}
              className={`${styles.actionButton} ${styles.demoButton}`}
              disabled={loading}
            >
              <span className={styles.actionIcon}>🎮</span>
              <div className={styles.actionContent}>
                <span className={styles.actionTitle}>Try Demo</span>
                <span className={styles.actionSubtitle}>Test the chat system</span>
              </div>
            </button>

            <button
              onClick={() => handleQuickScan('mbblsqjm-i24r2')}
              className={`${styles.actionButton} ${styles.businessButton}`}
              disabled={loading}
            >
              <span className={styles.actionIcon}>💼</span>
              <div className={styles.actionContent}>
                <span className={styles.actionTitle}>Business Center</span>
                <span className={styles.actionSubtitle}>Get assistance</span>
              </div>
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className={styles.instructions}>
          <h4 className={styles.instructionsTitle}>How to use:</h4>
          <ol className={styles.instructionsList}>
            <li>Select your preferred language</li>
            <li>Scan the QR code with your camera or enter it manually</li>
            <li>Start chatting with our staff</li>
            <li>Get help with hotel services</li>
          </ol>
        </div>

        {/* Features */}
        <div className={styles.features}>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>🌐</span>
            <span className={styles.featureText}>Multi-language Support</span>
          </div>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>🔒</span>
            <span className={styles.featureText}>Secure & Private</span>
          </div>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>⚡</span>
            <span className={styles.featureText}>Instant Response</span>
          </div>
          <div className={styles.feature}>
            <span className={styles.featureIcon}>📱</span>
            <span className={styles.featureText}>Mobile Optimized</span>
          </div>
        </div>

        {/* Footer */}
        <div className={styles.footer}>
          <p className={styles.footerText}>
            Need help? Contact our front desk at extension 0
          </p>
        </div>
      </div>
    </div>
  )
}
