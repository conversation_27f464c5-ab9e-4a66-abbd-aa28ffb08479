'use client';

import { useState, useEffect, useRef } from 'react';
import { ChatMessage, ChatSession } from '../../hooks/useChat';
import styles from './ChatInterface.improved.module.scss';

interface ChatInterfaceProps {
  sessionId: string;
  guestId: string;
  guestLanguage: string;
  messages: ChatMessage[];
  session: ChatSession | null;
  loading: boolean;
  connected: boolean;
  onSendMessage: (content: string) => Promise<boolean>;
  onStartTyping: () => void;
  onStopTyping: () => void;
  isTyping: boolean;
  error: string | null;
  realtimeConnected?: boolean;
  usePollingFallback?: boolean;
}

export function ChatInterface({
  sessionId,
  guestId,
  guestLanguage,
  messages,
  session,
  loading,
  connected,
  onSendMessage,
  onStartTyping,
  onStopTyping,
  isTyping,
  error,
  realtimeConnected = false,
  usePollingFallback = false
}: ChatInterfaceProps) {
  const [inputMessage, setInputMessage] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle send message
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || sending || !connected) {
      return;
    }

    setSending(true);
    onStopTyping();

    try {
      const success = await onSendMessage(inputMessage.trim());
      if (success) {
        setInputMessage('');
        inputRef.current?.focus();
      }
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setSending(false);
    }
  };

  // Handle input change with typing indicators
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputMessage(value);
    
    if (value.trim()) {
      onStartTyping();
    } else {
      onStopTyping();
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Get message display content (with translation if available)
  const getMessageContent = (message: ChatMessage) => {
    if (message.is_translated && message.translated_content && message.show_translation) {
      return {
        primary: message.translated_content,
        secondary: message.original_content,
        showBoth: true
      };
    }
    return {
      primary: message.content,
      secondary: null,
      showBoth: false
    };
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
          <p className="text-gray-500">Loading chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.chatInterface}>
      {/* Error Banner */}
      {error && (
        <div className={styles.errorBanner}>
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className={styles.messagesArea}>
        {/* Welcome Message */}
        {messages.length === 0 && (
          <div className={styles.welcomeMessage}>
            <div className={styles.icon}>💬</div>
            <h3 className={styles.title}>Welcome to LoaLoa Chat!</h3>
            <p className={styles.subtitle}>How can we assist you today?</p>
            <div className={styles.badges}>
              <div className={`${styles.badge} ${styles.connected}`}>
                ✓ Connected
              </div>
              <div className={`${styles.badge} ${styles.language}`}>
                🌐 {guestLanguage.toUpperCase()}
              </div>
              {session?.auto_translate && (
                <div className={`${styles.badge} ${styles.translation}`}>
                  🔄 Auto-translate
                </div>
              )}
            </div>
          </div>
        )}

        {/* Message List */}
        <div className={styles.messagesList}>
          {messages.map((message) => {
            const messageContent = getMessageContent(message);
            const isGuest = message.sender_type === 'guest';
            
            return (
              <div
                key={message.id}
                className={`${styles.messageContainer} ${isGuest ? styles.guest : styles.staff}`}
              >
                <div className={`${styles.messageBubble} ${isGuest ? styles.guest : styles.staff}`}>
                  {/* Sender Label */}
                  <div className={styles.messageHeader}>
                    {isGuest ? 'You' : 'Staff'} • {formatTime(message.created_at)}
                  </div>

                  {/* Message Content */}
                  <div className={styles.messageContent}>
                    <div>{messageContent.primary}</div>
                    {messageContent.showBoth && messageContent.secondary && (
                      <div className={styles.originalContent}>
                        Original: {messageContent.secondary}
                      </div>
                    )}
                  </div>

                  {/* Translation Info */}
                  {message.is_translated && (
                    <div className={styles.translationInfo}>
                      🔄 {message.original_language?.toUpperCase()} → {message.translated_language?.toUpperCase()}
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {/* Typing Indicator */}
          {isTyping && (
            <div className={styles.typingIndicator}>
              <div className={styles.bubble}>
                <div className={styles.dots}>
                  <div className={styles.dot}></div>
                  <div className={styles.dot}></div>
                  <div className={styles.dot}></div>
                  <span className={styles.text}>Staff is typing...</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className={styles.inputArea}>
        <div className={styles.inputContainer}>
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={connected ? "Type your message..." : "Connecting..."}
            disabled={!connected || sending}
            className={styles.messageInput}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !connected || sending}
            className={styles.sendButton}
          >
            {sending ? (
              <>
                <div className={styles.spinner}></div>
                <span className={styles.text}>Sending...</span>
              </>
            ) : (
              <>
                <span className={styles.text}>Send</span>
                <span>📤</span>
              </>
            )}
          </button>
        </div>

        {/* Enhanced Status indicators */}
        <div className={styles.statusBar}>
  <div className={styles.statusLeft}>
    <span className={`${styles.connectionStatus} ${connected ? styles.connected : styles.disconnected}`}>
      <div className={`${styles.indicator} ${connected ? styles.connected : styles.disconnected}`}></div>
      {connected ? 'Connected' : 'Disconnected'}
    </span>
    <span>Messages: {messages.length}</span>
    {connected && (
      <span
        className={styles.syncStatus}
        title={realtimeConnected ? "Real-time messaging active" : usePollingFallback ? "Polling fallback active" : "Connecting..."}
      >
        {realtimeConnected ? '⚡ Real-time' : usePollingFallback ? '🔄 Polling' : '🔌 Connecting'}
      </span>
    )}
  </div>
  <div>
    Press Enter to send
  </div>
</div>
      </div>
    </div>
  );
}

export default ChatInterface;