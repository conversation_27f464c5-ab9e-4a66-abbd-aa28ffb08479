.chatInputForm {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.inputContainer {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  margin-bottom: 12px;
}

.textareaContainer {
  flex: 1;
  position: relative;
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 8px 12px;
  transition: all 0.2s;

  &:focus-within {
    border-color: #f97316;
    background: white;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }

  .messageTextarea {
    width: 100%;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.5;
    color: #111827;
    min-height: 40px;
    max-height: 120px;
    padding-right: 80px;

    &::placeholder {
      color: #9ca3af;
    }

    &:disabled {
      color: #9ca3af;
      cursor: not-allowed;
    }
  }

  .inputActions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;

    button {
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.2s;

      &:hover {
        background: #f3f4f6;
      }
    }
  }
}

.sendContainer {
  .sendButton {
    width: 44px;
    height: 44px;
    background: #f97316;
    border: none;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 18px;

    &:hover:not(:disabled) {
      background: #ea580c;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
    }

    &:disabled {
      background: #d1d5db;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .sendIcon {
      display: block;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }

    &:hover:not(:disabled) .sendIcon {
      transform: rotate(15deg);
    }
  }
}

.inputFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

.translationToggle {
  .toggleLabel {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
    color: #374151;
    user-select: none;

    .toggleCheckbox {
      display: none;
    }

    .toggleSlider {
      position: relative;
      width: 40px;
      height: 20px;
      background: #d1d5db;
      border-radius: 20px;
      transition: all 0.3s;

      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background: white;
        border-radius: 50%;
        transition: all 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .toggleCheckbox:checked + .toggleSlider {
      background: #f97316;

      &::before {
        transform: translateX(20px);
      }
    }

    .toggleText {
      font-weight: 500;
    }
  }
}

.quickActions {
  display: flex;
  gap: 6px;

  .quickAction {
    padding: 4px 8px;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    color: #374151;

    &:hover {
      background: #e5e7eb;
      border-color: #d1d5db;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .inputFooter {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .quickActions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}
