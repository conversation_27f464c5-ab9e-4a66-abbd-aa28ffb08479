// Rate limiting utility to prevent API spam
// Tiện ích giới hạn tốc độ để ngăn chặn spam API

interface RateLimitInfo {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private requests = new Map<string, RateLimitInfo>();
  
  // Clean up expired entries every minute
  // Dọn dẹp các entries hết hạn mỗi phút
  constructor() {
    setInterval(() => this.cleanup(), 60000);
  }
  
  isRateLimited(
    identifier: string, 
    windowMs: number = 60000, // 1 minute window
    maxRequests: number = 100 // Max 100 requests per minute
  ): boolean {
    const now = Date.now();
    const info = this.requests.get(identifier);
    
    // If no previous requests or window expired, start fresh
    // N<PERSON><PERSON> không có request trước đó hoặc window đã hết hạn, bắt đầu mới
    if (!info || now > info.resetTime) {
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      });
      return false;
    }
    
    // Check if limit exceeded
    // Ki<PERSON><PERSON> tra có vượt quá giới hạn không
    if (info.count >= maxRequests) {
      return true; // Rate limited
    }
    
    // Increment count
    // Tăng số lượng request
    info.count++;
    this.requests.set(identifier, info);
    
    return false;
  }
  
  getRemainingRequests(identifier: string, maxRequests: number = 100): number {
    const info = this.requests.get(identifier);
    if (!info || Date.now() > info.resetTime) {
      return maxRequests;
    }
    return Math.max(0, maxRequests - info.count);
  }
  
  private cleanup() {
    const now = Date.now();
    for (const [key, info] of this.requests.entries()) {
      if (now > info.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Helper function to get client identifier
// Hàm helper để lấy định danh client
export function getClientIdentifier(request: Request): string {
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';
  
  // Add session info if available for more granular limiting
  // Thêm thông tin session nếu có để giới hạn chi tiết hơn
  const userAgent = request.headers.get('user-agent') || '';
  return `${ip}-${userAgent.slice(0, 50)}`;
}