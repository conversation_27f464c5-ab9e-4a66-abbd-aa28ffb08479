import bcrypt from 'bcryptjs';

/**
 * Hash a plain text password using bcrypt
 * Tạo hash cho password sử dụng bcrypt với salt rounds = 10
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * Verify a plain text password against a hashed password
 * Kiểm tra password plain text với password đã hash
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * Generate a session token for authenticated staff
 * Tạo session token cho staff đã đăng nhập
 */
export function generateSessionToken(userId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `staff_${userId}_${timestamp}_${random}`;
}

/**
 * Validate session token format
 * Kiểm tra định dạng session token
 */
export function validateSessionTokenFormat(token: string): boolean {
  const pattern = /^staff_[a-f0-9-]{36}_\d+_[a-z0-9]+$/;
  return pattern.test(token);
}
