'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClient } from '@supabase/supabase-js';

// Types
export interface ChatMessage {
  id: string;
  chat_session_id: string;
  sender_type: 'guest' | 'staff';
  sender_id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  original_language?: string;
  translated_language?: string;
  is_translated: boolean;
  show_translation: boolean;
  created_at: string;
  metadata?: any;
}

export interface ChatSession {
  id: string;
  tenant_id: string;
  guest_id?: string;
  status: 'active' | 'ended' | 'pending';
  guest_language?: string;
  staff_language?: string;
  auto_translate: boolean;
  source_type?: string;
  priority: string;
  created_at: string;
  updated_at: string;
}

export interface UseChatOptions {
  sessionId: string;
  guestId: string;
  autoTranslate?: boolean;
  guestLanguage?: string;
}

export interface UseChatReturn {
  messages: ChatMessage[];
  session: ChatSession | null;
  loading: boolean;
  connected: boolean;
  isTyping: boolean;
  sendMessage: (content: string) => Promise<boolean>;
  startTyping: () => void;
  stopTyping: () => void;
  refresh: () => Promise<void>;
  setAutoTranslate: (enabled: boolean) => void;
  error: string | null;
}

export function useChat({ 
  sessionId, 
  guestId, 
  autoTranslate = true,
  guestLanguage = 'en'
}: UseChatOptions): UseChatReturn {
  // State management
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [session, setSession] = useState<ChatSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for cleanup
  const supabaseRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize Supabase client
  useEffect(() => {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      supabaseRef.current = createClient(supabaseUrl, supabaseKey);
      setConnected(true);
    } else {
      setError('Supabase configuration missing');
      setConnected(false);
    }
  }, []);

  // Load initial session data
  const loadSession = useCallback(async () => {
    if (!supabaseRef.current || !sessionId) return;

    try {
      setLoading(true);
      
      // Load session
      const { data: sessionData, error: sessionError } = await supabaseRef.current
        .from('tenant_chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        throw new Error(`Failed to load session: ${sessionError.message}`);
      }

      setSession(sessionData);

      // Load messages
      const { data: messagesData, error: messagesError } = await supabaseRef.current
        .from('tenant_chat_messages')
        .select('*')
        .eq('chat_session_id', sessionId)
        .order('created_at', { ascending: true });

      if (messagesError) {
        throw new Error(`Failed to load messages: ${messagesError.message}`);
      }

      setMessages(messagesData || []);
      setError(null);

    } catch (err) {
      console.error('Error loading session:', err);
      setError(err instanceof Error ? err.message : 'Failed to load chat session');
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  // Setup real-time subscriptions
  useEffect(() => {
    if (!supabaseRef.current || !sessionId) return;

    const messagesSubscription = supabaseRef.current
      .channel(`messages:${sessionId}`)
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'tenant_chat_messages',
          filter: `chat_session_id=eq.${sessionId}`
        },
        (payload: any) => {
          const newMessage = payload.new as ChatMessage;
          setMessages(prev => [...prev, newMessage]);
        }
      )
      .on('postgres_changes', 
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'tenant_chat_messages',
          filter: `chat_session_id=eq.${sessionId}`
        },
        (payload: any) => {
          const updatedMessage = payload.new as ChatMessage;
          setMessages(prev => 
            prev.map(msg => msg.id === updatedMessage.id ? updatedMessage : msg)
          );
        }
      )
      .subscribe();

    // Session status subscription
    const sessionSubscription = supabaseRef.current
      .channel(`session:${sessionId}`)
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public', 
          table: 'tenant_chat_sessions',
          filter: `id=eq.${sessionId}`
        },
        (payload: any) => {
          setSession(payload.new as ChatSession);
        }
      )
      .subscribe();

    return () => {
      messagesSubscription.unsubscribe();
      sessionSubscription.unsubscribe();
    };
  }, [sessionId]);

  // Load initial data
  useEffect(() => {
    loadSession();
  }, [loadSession]);

  // Send message function
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!supabaseRef.current || !sessionId || !guestId || !content.trim()) {
      return false;
    }

    try {
      const messageData = {
        chat_session_id: sessionId,
        sender_type: 'guest' as const,
        sender_id: guestId,
        content: content.trim(),
        original_content: content.trim(),
        original_language: guestLanguage,
        is_translated: false,
        show_translation: autoTranslate,
        created_at: new Date().toISOString(),
        metadata: {}
      };

      const { error } = await supabaseRef.current
        .from('tenant_chat_messages')
        .insert([messageData]);

      if (error) {
        throw new Error(`Failed to send message: ${error.message}`);
      }

      // Simulate staff auto-reply after 2-5 seconds (for demo)
      setTimeout(async () => {
        const staffReply = generateStaffReply(content);
        const staffMessageData = {
          chat_session_id: sessionId,
          sender_type: 'staff' as const,
          sender_id: 'demo-staff-001',
          content: staffReply,
          original_content: staffReply,
          original_language: 'en',
          is_translated: false,
          show_translation: autoTranslate,
          created_at: new Date().toISOString(),
          metadata: { auto_generated: true }
        };

        await supabaseRef.current
          .from('tenant_chat_messages')
          .insert([staffMessageData]);
      }, Math.random() * 3000 + 2000);

      return true;

    } catch (err) {
      console.error('Error sending message:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
      return false;
    }
  }, [sessionId, guestId, guestLanguage, autoTranslate]);

  // Typing indicators
  const startTyping = useCallback(() => {
    setIsTyping(true);
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, []);

  const stopTyping = useCallback(() => {
    setIsTyping(false);
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, []);

  // Auto-stop typing after 3 seconds
  useEffect(() => {
    if (isTyping) {
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
      }, 3000);
    }
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [isTyping]);

  // Update auto-translate setting
  const setAutoTranslate = useCallback(async (enabled: boolean) => {
    if (!supabaseRef.current || !sessionId) return;

    try {
      const { error } = await supabaseRef.current
        .from('tenant_chat_sessions')
        .update({ auto_translate: enabled })
        .eq('id', sessionId);

      if (error) {
        throw new Error(`Failed to update translation setting: ${error.message}`);
      }
    } catch (err) {
      console.error('Error updating auto-translate:', err);
    }
  }, [sessionId]);

  // Refresh data
  const refresh = useCallback(async () => {
    await loadSession();
  }, [loadSession]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    messages,
    session,
    loading,
    connected,
    isTyping,
    sendMessage,
    startTyping,
    stopTyping,
    refresh,
    setAutoTranslate,
    error
  };
}

// Helper function to generate staff replies (for demo)
function generateStaffReply(guestMessage: string): string {
  const lowerMessage = guestMessage.toLowerCase();
  
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return "Hello! Welcome to our hotel. How can I assist you today?";
  }
  
  if (lowerMessage.includes('room service') || lowerMessage.includes('food')) {
    return "I'd be happy to help with room service! What would you like to order?";
  }
  
  if (lowerMessage.includes('wifi') || lowerMessage.includes('internet')) {
    return "Our WiFi network is 'GuestNet' and the password is 'Welcome123'. Let me know if you need help connecting!";
  }
  
  if (lowerMessage.includes('checkout') || lowerMessage.includes('check out')) {
    return "Checkout time is 11:00 AM. Would you like me to arrange a late checkout or help with your luggage?";
  }
  
  if (lowerMessage.includes('restaurant') || lowerMessage.includes('dining')) {
    return "Our restaurant is open from 6:00 AM to 10:00 PM. Would you like me to make a reservation for you?";
  }
  
  return "Thank you for your message! Our staff will assist you shortly. Is there anything else I can help you with?";
}

export default useChat;
