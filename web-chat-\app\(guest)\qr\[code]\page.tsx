'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams, useSearchParams } from 'next/navigation'
import styles from './QRScan.module.scss'

interface QRScanResponse {
  success: boolean
  qr_code: {
    id: string
    location: string
    room_number?: string
    tenant_id: string
    display_name: string
  }
  temporary_user: {
    id: string
    session_token: string
    preferred_language: string
    expires_at: string
    display_name: string
    is_existing: boolean
  }
  redirect_url: string
  error?: string
}

// Generate device fingerprint helper function
const generateDeviceFingerprint = (): string => {
  if (typeof window === 'undefined') return 'server-side'

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx?.fillText('Device fingerprint', 2, 2)

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')

  return btoa(fingerprint).substring(0, 32)
}

export default function QRScanPage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [qrData, setQrData] = useState<QRScanResponse | null>(null)
  const [deviceId, setDeviceId] = useState<string>('')

  const code = params?.code as string
  const lang = searchParams?.get('lang') || 'en'

  useEffect(() => {
    // Generate device ID on client side
    if (typeof window !== 'undefined' && !deviceId) {
      const device = searchParams?.get('device') || generateDeviceFingerprint()
      setDeviceId(device)
    }
  }, [searchParams, deviceId])

  useEffect(() => {
    if (!code) {
      setError('QR code not found')
      setLoading(false)
      return
    }

    if (deviceId) {
      scanQRCode()
    }
  }, [code, deviceId])

  const scanQRCode = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/qr-scan/${code}?lang=${lang}&device=${deviceId}`)
      const data: QRScanResponse = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to scan QR code')
      }

      if (data.success && data.redirect_url) {
        setQrData(data)
        
        // Show success message briefly before redirect
        setTimeout(() => {
          router.push(data.redirect_url)
        }, 1500)
      } else {
        throw new Error('Invalid QR code response')
      }

    } catch (err) {
      console.error('QR scan error:', err)
      setError(err instanceof Error ? err.message : 'Failed to scan QR code')
      setLoading(false)
    }
  }



  const handleRetry = () => {
    scanQRCode()
  }

  const handleGoHome = () => {
    router.push('/')
  }

  if (loading || !deviceId) {
    return (
      <div className={styles.container}>
        <div className={styles.loadingCard}>
          <div className={styles.spinner}>
            <div className={styles.spinnerRing}></div>
          </div>
          <h2 className={styles.title}>Scanning QR Code...</h2>
          <p className={styles.subtitle}>Please wait while we process your request</p>
          <div className={styles.qrCode}>
            <span className={styles.qrIcon}>📱</span>
            <code className={styles.codeValue}>{code}</code>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorCard}>
          <div className={styles.errorIcon}>❌</div>
          <h2 className={styles.errorTitle}>QR Code Error</h2>
          <p className={styles.errorMessage}>{error}</p>
          <div className={styles.errorActions}>
            <button 
              onClick={handleRetry}
              className={`${styles.button} ${styles.buttonPrimary}`}
            >
              Try Again
            </button>
            <button 
              onClick={handleGoHome}
              className={`${styles.button} ${styles.buttonSecondary}`}
            >
              Go Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (qrData) {
    return (
      <div className={styles.container}>
        <div className={styles.successCard}>
          <div className={styles.successIcon}>✅</div>
          <h2 className={styles.successTitle}>QR Code Scanned Successfully!</h2>
          <div className={styles.qrInfo}>
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Location:</span>
              <span className={styles.infoValue}>{qrData.qr_code.display_name}</span>
            </div>
            {qrData.qr_code.room_number && (
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Room:</span>
                <span className={styles.infoValue}>{qrData.qr_code.room_number}</span>
              </div>
            )}
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>Language:</span>
              <span className={styles.infoValue}>{qrData.temporary_user.preferred_language.toUpperCase()}</span>
            </div>
          </div>
          <div className={styles.redirectMessage}>
            <div className={styles.loadingDots}>
              <span></span>
              <span></span>
              <span></span>
            </div>
            <p>Redirecting to chat...</p>
          </div>
        </div>
      </div>
    )
  }

  return null
}
