'use client';

import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import styles from './error.module.scss';

export default function ErrorPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
      </div>
    );
  }

  const errorCode = searchParams.get('code') || 'unknown';
  const message = searchParams.get('message') || '';

  const getErrorInfo = () => {
    switch (errorCode) {
      case 'license':
        return {
          icon: '🔐',
          title: 'License Error',
          message: message || 'Your license is invalid or expired. Please contact your administrator.',
          showRetry: false
        };
      case 'session':
        return {
          icon: '⏰',
          title: 'Session Expired',
          message: message || 'Your chat session has expired. Please scan the QR code again to start a new session.',
          showRetry: true
        };
      case 'connection':
        return {
          icon: '🌐',
          title: 'Connection Error',
          message: message || 'Unable to connect to the server. Please check your internet connection.',
          showRetry: true
        };
      case 'qr':
        return {
          icon: '📱',
          title: 'Invalid QR Code',
          message: message || 'The QR code you scanned is invalid or expired. Please try scanning again.',
          showRetry: false
        };
      case 'tenant':
        return {
          icon: '🏨',
          title: 'Hotel Configuration Error',
          message: message || 'This hotel\'s chat system is not properly configured. Please contact hotel staff.',
          showRetry: false
        };
      case 'system':
        return {
          icon: '⚙️',
          title: 'System Error',
          message: message || 'A system error occurred. Our technical team has been notified.',
          showRetry: true
        };
      default:
        return {
          icon: '❌',
          title: 'Something Went Wrong',
          message: message || 'An unexpected error occurred. Please try again or contact support.',
          showRetry: true
        };
    }
  };

  const errorInfo = getErrorInfo();

  const handleRetry = () => {
    // Attempt to go back to previous page or home
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <div className={styles.errorPage}>
      <div className={styles.errorContainer}>
        <div className={styles.errorIcon}>
          {errorInfo.icon}
        </div>
        
        <h1 className={styles.errorTitle}>
          {errorInfo.title}
        </h1>
        
        <p className={styles.errorMessage}>
          {errorInfo.message}
        </p>
        
        {errorCode !== 'unknown' && (
          <div className={styles.errorCode}>
            Error Code: {errorCode.toUpperCase()}
          </div>
        )}
        
        <div className={styles.buttonGroup}>
          {errorInfo.showRetry && (
            <button
              onClick={handleRetry}
              className={styles.primaryButton}
            >
              🔄 Try Again
            </button>
          )}
          
          <Link href="/" className={styles.secondaryButton}>
            🏠 Back to Home
          </Link>
          
          {errorCode === 'license' && (
            <Link href="/activate" className={styles.primaryButton}>
              🔐 Activate License
            </Link>
          )}
          
          {errorCode === 'qr' && (
            <Link href="/qr/demo" className={styles.secondaryButton}>
              📱 Try Demo QR
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
