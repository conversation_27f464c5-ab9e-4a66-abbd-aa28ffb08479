'use client';

import { useChat } from '../../hooks/useChat';
import { useEffect, useState } from 'react';

export default function TestChatPage() {
  const [sessionId, setSessionId] = useState<string>('');
  const [guestId, setGuestId] = useState<string>('');
  const [message, setMessage] = useState<string>('');

  const { 
    messages, 
    session, 
    loading, 
    connected, 
    sendMessage, 
    error 
  } = useChat({
    sessionId,
    guestId,
    autoTranslate: true,
    guestLanguage: 'en'
  });

  useEffect(() => {
    // Generate test IDs
    setSessionId('test-session-001');
    setGuestId('test-guest-001');
  }, []);

  const handleSendMessage = async () => {
    if (message.trim()) {
      const success = await sendMessage(message);
      if (success) {
        setMessage('');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg">
        <div className="p-4 border-b">
          <h1 className="text-lg font-semibold">Chat Hook Test</h1>
          <div className="text-sm text-gray-600 mt-1">
            Connected: {connected ? '✅' : '❌'} | 
            Loading: {loading ? '⏳' : '✅'} |
            Messages: {messages.length}
          </div>
          {error && (
            <div className="text-red-600 text-sm mt-2">
              Error: {error}
            </div>
          )}
        </div>

        <div className="p-4 h-64 overflow-y-auto">
          {messages.map((msg) => (
            <div 
              key={msg.id}
              className={`mb-2 p-2 rounded ${
                msg.sender_type === 'guest' 
                  ? 'bg-blue-100 ml-8' 
                  : 'bg-gray-100 mr-8'
              }`}
            >
              <div className="text-xs text-gray-500 mb-1">
                {msg.sender_type === 'guest' ? 'You' : 'Staff'} • {
                  new Date(msg.created_at).toLocaleTimeString()
                }
              </div>
              <div>{msg.content}</div>
            </div>
          ))}
        </div>

        <div className="p-4 border-t">
          <div className="flex gap-2">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type your message..."
              className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleSendMessage}
              disabled={!message.trim() || loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
