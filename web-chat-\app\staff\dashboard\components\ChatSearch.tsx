'use client';

import { useState, useEffect, useRef } from 'react';
import styles from './ChatSearch.module.scss';

interface ChatSearchProps {
  onSearch: (query: string) => void;
  onFilter: (filters: ChatFilters) => void;
  totalChats: number;
  activeFilters: ChatFilters;
}

interface ChatFilters {
  status: 'all' | 'active' | 'pending' | 'waiting';
  priority: 'all' | 'low' | 'normal' | 'high' | 'urgent';
  language: 'all' | 'en' | 'vi' | 'ko' | 'ja' | 'es' | 'fr' | 'de' | 'th' | 'id';
  timeRange: 'all' | 'today' | 'week' | 'month';
  unreadOnly: boolean;
}

export default function ChatSearch({ 
  onSearch, 
  onFilter, 
  totalChats, 
  activeFilters 
}: ChatSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<ChatFilters>(activeFilters);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Debounce search - Tìm kiếm với độ trễ
    const timer = setTimeout(() => {
      onSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, onSearch]);

  const handleFilterChange = <K extends keyof ChatFilters>(
    key: K, 
    value: ChatFilters[K]
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilter(newFilters);
  };

  const clearAllFilters = () => {
    const defaultFilters: ChatFilters = {
      status: 'all',
      priority: 'all',
      language: 'all',
      timeRange: 'all',
      unreadOnly: false
    };
    setFilters(defaultFilters);
    onFilter(defaultFilters);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status !== 'all') count++;
    if (filters.priority !== 'all') count++;
    if (filters.language !== 'all') count++;
    if (filters.timeRange !== 'all') count++;
    if (filters.unreadOnly) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className={styles.chatSearch}>
      <div className={styles.searchContainer}>
        <div className={styles.searchInputGroup}>
          <div className={styles.searchIcon}>🔍</div>
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search chats by guest name, room number..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={styles.searchInput}
          />
          {searchQuery && (
            <button
              className={styles.clearSearch}
              onClick={() => setSearchQuery('')}
            >
              ✕
            </button>
          )}
        </div>

        <div className={styles.searchActions}>
          <button
            className={`${styles.filterButton} ${showFilters ? styles.active : ''}`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <span className={styles.filterIcon}>⚙️</span>
            Filter
            {activeFilterCount > 0 && (
              <span className={styles.filterBadge}>{activeFilterCount}</span>
            )}
          </button>

          <div className={styles.chatCount}>
            <span className={styles.count}>{totalChats}</span>
            <span className={styles.label}>chats</span>
          </div>
        </div>
      </div>

      {showFilters && (
        <div className={styles.filtersPanel}>
          <div className={styles.filtersHeader}>
            <h4>Filter Chats</h4>
            <div className={styles.filtersActions}>
              {activeFilterCount > 0 && (
                <button
                  className={styles.clearFilters}
                  onClick={clearAllFilters}
                >
                  Clear All
                </button>
              )}
              <button
                className={styles.closeFilters}
                onClick={() => setShowFilters(false)}
              >
                ✕
              </button>
            </div>
          </div>

          <div className={styles.filtersGrid}>
            {/* Status Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Status</label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value as any)}
                className={styles.filterSelect}
              >
                <option value="all">All Status</option>
                <option value="active">🟢 Active</option>
                <option value="pending">🟡 Pending</option>
                <option value="waiting">🔴 Waiting</option>
              </select>
            </div>

            {/* Priority Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Priority</label>
              <select
                value={filters.priority}
                onChange={(e) => handleFilterChange('priority', e.target.value as any)}
                className={styles.filterSelect}
              >
                <option value="all">All Priority</option>
                <option value="urgent">🚨 Urgent</option>
                <option value="high">🔴 High</option>
                <option value="normal">🔵 Normal</option>
                <option value="low">⚫ Low</option>
              </select>
            </div>

            {/* Language Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Language</label>
              <select
                value={filters.language}
                onChange={(e) => handleFilterChange('language', e.target.value as any)}
                className={styles.filterSelect}
              >
                <option value="all">All Languages</option>
                <option value="en">🇺🇸 English</option>
                <option value="vi">🇻🇳 Vietnamese</option>
                <option value="ko">🇰🇷 Korean</option>
                <option value="ja">🇯🇵 Japanese</option>
                <option value="es">🇪🇸 Spanish</option>
                <option value="fr">🇫🇷 French</option>
                <option value="de">🇩🇪 German</option>
                <option value="th">🇹🇭 Thai</option>
                <option value="id">🇮🇩 Indonesian</option>
              </select>
            </div>

            {/* Time Range Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Time Range</label>
              <select
                value={filters.timeRange}
                onChange={(e) => handleFilterChange('timeRange', e.target.value as any)}
                className={styles.filterSelect}
              >
                <option value="all">All Time</option>
                <option value="today">📅 Today</option>
                <option value="week">📆 This Week</option>
                <option value="month">🗓️ This Month</option>
              </select>
            </div>
          </div>

          {/* Quick Filters */}
          <div className={styles.quickFilters}>
            <label className={styles.filterLabel}>Quick Filters</label>
            <div className={styles.quickFilterButtons}>
              <button
                className={`${styles.quickFilter} ${filters.unreadOnly ? styles.active : ''}`}
                onClick={() => handleFilterChange('unreadOnly', !filters.unreadOnly)}
              >
                <span className={styles.quickFilterIcon}>📩</span>
                Unread Only
                {filters.unreadOnly && <span className={styles.checkIcon}>✓</span>}
              </button>

              <button
                className={`${styles.quickFilter} ${filters.priority === 'urgent' ? styles.active : ''}`}
                onClick={() => handleFilterChange('priority', filters.priority === 'urgent' ? 'all' : 'urgent')}
              >
                <span className={styles.quickFilterIcon}>🚨</span>
                Urgent Only
                {filters.priority === 'urgent' && <span className={styles.checkIcon}>✓</span>}
              </button>

              <button
                className={`${styles.quickFilter} ${filters.status === 'waiting' ? styles.active : ''}`}
                onClick={() => handleFilterChange('status', filters.status === 'waiting' ? 'all' : 'waiting')}
              >
                <span className={styles.quickFilterIcon}>⏰</span>
                Waiting Response
                {filters.status === 'waiting' && <span className={styles.checkIcon}>✓</span>}
              </button>
            </div>
          </div>

          {/* Filter Summary */}
          {activeFilterCount > 0 && (
            <div className={styles.filterSummary}>
              <span className={styles.summaryText}>
                {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} applied
              </span>
              <span className={styles.summaryCount}>
                Showing {totalChats} result{totalChats !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
