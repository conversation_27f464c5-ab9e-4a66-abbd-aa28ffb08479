'use client';

import { useState, useEffect } from 'react';
import styles from './ChatStats.module.scss';

interface ChatStatsProps {
  activeSessions: any[];
  selectedSession: string | null;
}

interface StatsData {
  totalChats: number;
  activeChats: number;
  pendingChats: number;
  urgentChats: number;
  unreadMessages: number;
  averageResponseTime: string;
  todayChats: number;
  satisfaction: number;
}

export default function ChatStats({ activeSessions, selectedSession }: ChatStatsProps) {
  const [stats, setStats] = useState<StatsData>({
    totalChats: 0,
    activeChats: 0,
    pendingChats: 0,
    urgentChats: 0,
    unreadMessages: 0,
    averageResponseTime: '0m',
    todayChats: 0,
    satisfaction: 4.8
  });

  const [showDetailedStats, setShowDetailedStats] = useState(false);

  useEffect(() => {
    // Calculate stats from active sessions - <PERSON><PERSON><PERSON> toán thống kê từ sessions
    const totalUnread = activeSessions.reduce((sum, s) => sum + s.unread_count, 0);
    const urgentCount = activeSessions.filter(s => s.priority === 'urgent' || s.priority === 'high').length;

    // Calculate average response time based on session count (mock calculation)
    const avgResponseMinutes = activeSessions.length > 0
      ? Math.max(1, Math.floor(2 + (activeSessions.length / 5)))
      : 0;

    const newStats: StatsData = {
      totalChats: activeSessions.length,
      activeChats: activeSessions.filter(s => s.status === 'active').length,
      pendingChats: activeSessions.filter(s => s.status === 'pending').length,
      urgentChats: urgentCount,
      unreadMessages: totalUnread,
      averageResponseTime: `${avgResponseMinutes}m`,
      todayChats: activeSessions.length, // Remove random component
      satisfaction: 4.8
    };

    setStats(newStats);
  }, [activeSessions]);

  const getStatColor = (value: number, max: number) => {
    const percentage = (value / max) * 100;
    if (percentage >= 80) return '#ef4444'; // Red for high values
    if (percentage >= 50) return '#f59e0b'; // Yellow for medium values
    return '#10b981'; // Green for low values
  };

  return (
    <div className={styles.chatStats}>
      <div className={styles.statsHeader}>
        <h3>Dashboard Stats</h3>
        <button
          className={styles.toggleButton}
          onClick={() => setShowDetailedStats(!showDetailedStats)}
        >
          {showDetailedStats ? '📊' : '📈'}
        </button>
      </div>

      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>💬</div>
          <div className={styles.statContent}>
            <span className={styles.statValue}>{stats.totalChats}</span>
            <span className={styles.statLabel}>Active Chats</span>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>📩</div>
          <div className={styles.statContent}>
            <span 
              className={styles.statValue}
              style={{ color: stats.unreadMessages > 5 ? '#ef4444' : '#10b981' }}
            >
              {stats.unreadMessages}
            </span>
            <span className={styles.statLabel}>Unread</span>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>🚨</div>
          <div className={styles.statContent}>
            <span 
              className={styles.statValue}
              style={{ color: getStatColor(stats.urgentChats, stats.totalChats) }}
            >
              {stats.urgentChats}
            </span>
            <span className={styles.statLabel}>Priority</span>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⏱️</div>
          <div className={styles.statContent}>
            <span className={styles.statValue}>{stats.averageResponseTime}</span>
            <span className={styles.statLabel}>Avg Response</span>
          </div>
        </div>
      </div>

      {showDetailedStats && (
        <div className={styles.detailedStats}>
          <div className={styles.statsRow}>
            <div className={styles.statDetail}>
              <span className={styles.detailLabel}>Today's Chats</span>
              <span className={styles.detailValue}>{stats.todayChats}</span>
            </div>
            <div className={styles.statDetail}>
              <span className={styles.detailLabel}>Satisfaction</span>
              <span className={styles.detailValue}>
                {stats.satisfaction} ⭐
              </span>
            </div>
          </div>

          <div className={styles.statusBreakdown}>
            <h4>Status Breakdown</h4>
            <div className={styles.progressBars}>
              <div className={styles.progressItem}>
                <span className={styles.progressLabel}>Active ({stats.activeChats})</span>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ 
                      width: `${(stats.activeChats / stats.totalChats) * 100}%`,
                      backgroundColor: '#10b981'
                    }}
                  />
                </div>
              </div>
              
              <div className={styles.progressItem}>
                <span className={styles.progressLabel}>Pending ({stats.pendingChats})</span>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progressFill}
                    style={{ 
                      width: `${(stats.pendingChats / stats.totalChats) * 100}%`,
                      backgroundColor: '#f59e0b'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
