import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase';

// GET /api/messages - Get messages for a session
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const session_id = searchParams.get('session_id');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!session_id) {
      return NextResponse.json(
        { success: false, error: 'session_id is required' },
        { status: 400 }
      );
    }

    console.log('📨 Loading messages for session:', session_id);

    const supabase = createServerSupabase();

    // Get messages for the session
    const { data: messages, error: messagesError } = await supabase
      .from('tenant_chat_messages')
      .select('*')
      .eq('chat_session_id', session_id)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (messagesError) {
      console.error('❌ Error loading messages:', messagesError);
      return NextResponse.json(
        { success: false, error: 'Failed to load messages' },
        { status: 500 }
      );
    }

    console.log(`✅ Loaded ${messages?.length || 0} messages`);

    return NextResponse.json({
      success: true,
      messages: messages || []
    });

  } catch (error) {
    console.error('❌ Error in messages GET API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/messages - Send a new message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      session_id,
      content,
      sender_type = 'guest',
      sender_name = 'Guest'
    } = body;

    if (!session_id || !content?.trim()) {
      return NextResponse.json(
        { success: false, error: 'session_id and content are required' },
        { status: 400 }
      );
    }

    console.log('📤 Sending message:', {
      session_id,
      sender_type,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
    });

    const supabase = createServerSupabase();

    // First verify the session exists
    const { data: session, error: sessionError } = await supabase
      .from('tenant_chat_sessions')
      .select('id, tenant_id, status, guest_language, auto_translate')
      .eq('id', session_id)
      .single();

    if (sessionError || !session) {
      console.error('❌ Session not found:', sessionError);
      return NextResponse.json(
        { success: false, error: 'Chat session not found' },
        { status: 404 }
      );
    }

    if (session.status !== 'active') {
      return NextResponse.json(
        { success: false, error: 'Chat session is not active' },
        { status: 400 }
      );
    }

    // Create the message
    const messageData = {
      chat_session_id: session_id,
      tenant_id: session.tenant_id, // Add tenant_id for realtime filtering
      sender_type,
      sender_name,
      content: content.trim(),
      original_content: content.trim(),
      is_translated: false,
      original_language: session.guest_language,
      show_translation: false,
      created_at: new Date().toISOString()
    };

    const { data: message, error: messageError } = await supabase
      .from('tenant_chat_messages')
      .insert(messageData)
      .select()
      .single();

    if (messageError) {
      console.error('❌ Error creating message:', messageError);
      return NextResponse.json(
        { success: false, error: 'Failed to send message' },
        { status: 500 }
      );
    }

    // Update session's updated_at timestamp
    await supabase
      .from('tenant_chat_sessions')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', session_id);

    console.log('✅ Message sent successfully:', message.id);

    return NextResponse.json({
      success: true,
      message
    });

  } catch (error) {
    console.error('❌ Error in messages POST API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
