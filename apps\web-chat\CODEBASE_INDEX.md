# 📋 LoaLoa Web Chat - Codebase Index

## 🏗️ Kiến trúc tổng quan

**LoaLoa Web Chat** là một nền tảng chat đa ngôn ngữ cho khách sạn với tính năng dịch thuật thời gian thực và định tuyến thông minh.

### 🛠️ Tech Stack
- **Frontend**: Next.js 14, TypeScript, SCSS Modules
- **Realtime**: Supabase Realtime + WebSocket
- **Database**: PostgreSQL (via Supabase)
- **Translation**: Multi-provider support
- **Styling**: Custom SCSS với animations
- **Components**: @loaloa/ui package

---

## 📁 C<PERSON>u trúc thư mục

```
apps/web-chat/
├── app/
│   ├── components/          # React Components
│   ├── hooks/              # Custom Hooks
│   ├── api/                # API Routes
│   ├── lib/                # Utilities & Services
│   ├── types/              # TypeScript Types
│   ├── staff/              # Staff Dashboard
│   ├── (guest)/            # Guest Pages
│   └── debug/              # Debug Tools
├── public/                 # Static Assets
├── middleware.ts           # Next.js Middleware
└── package.json           # Dependencies
```

---

## 🧩 React Components

### 💬 Chat Components
- **`ChatInterface.tsx`** - Main chat interface component
  - Props: sessionId, guestId, messages, onSendMessage, etc.
  - Features: Auto-scroll, typing indicators, translation toggle
  - Styles: `ChatInterface.improved.module.scss`

### 🌐 Language Components
- **`LanguageSelector.tsx`** - Language selection dropdown
  - Props: currentLanguage, onLanguageChange, availableLanguages
  - Features: Search, native names, RTL support
  - Styles: `LanguageSelector.module.scss`

- **`TranslationToggle.tsx`** - Toggle translation on/off
  - Props: enabled, onToggle, showLabels
  - Styles: `TranslationToggle.module.scss`

### 👥 Staff Components
- **`staff/`** directory contains staff-specific components
  - Dashboard components
  - Session management
  - Message handling

---

## 🎣 Custom Hooks

### 📡 Chat Hooks
- **`useChat.ts`** - Standard chat hook
  - Real-time messaging với Supabase
  - Message management
  - Connection status

- **`useChat.enhanced.ts`** - Enhanced version với monitoring
  - Performance tracking
  - Fallback mechanisms
  - Debug capabilities

- **`useChat.realtime.ts`** - Realtime-focused implementation
  - WebSocket optimization
  - Connection recovery
  - Polling fallback

---

## 🔌 API Routes

### 💬 Chat Sessions (`/api/chat-sessions/`)
- **`POST /api/chat-sessions`** - Create new chat session
- **`GET /api/chat-sessions`** - List sessions
- **`GET /api/chat-sessions/[id]`** - Get specific session
- **`POST /api/chat-sessions/check`** - Check existing sessions

### 📨 Messages (`/api/messages/`)
- **`GET /api/messages`** - Get messages for session
- **`POST /api/messages`** - Send new message

### 📱 QR Scan (`/api/qr-scan/`)
- **`GET /api/qr-scan/[code]`** - Process QR code scan
  - Validates QR code
  - Creates temporary user
  - Returns redirect URL

### ⚡ Realtime (`/api/realtime/`)
- **`POST /api/realtime/setup`** - Setup realtime subscriptions
- **`GET /api/realtime/setup`** - Check realtime status

### 👤 Staff Auth (`/api/staff/auth/`)
- **`POST /api/staff/auth/login`** - Staff login

---

## 🛠️ Utilities & Services

### 🗄️ Supabase Integration (`/lib/supabase/`)
- **`index.ts`** - Main Supabase client factory
- **`client.ts`** - Client-side Supabase client
- **`server.ts`** - Server-side Supabase client
- **`admin.ts`** - Admin client với service role

### 🔐 Authentication (`/lib/auth/`)
- **`password.ts`** - Password hashing & verification
  - `hashPassword(password)` - Hash password với bcrypt
  - `verifyPassword(password, hash)` - Verify password
  - `generateSessionToken()` - Generate session tokens

### 🎯 Chat Routing (`/lib/services/`)
- **`chatRouting.ts`** - Smart message routing service
  - `determineRouting(context)` - Determine routing rules
  - `assignToReceptionPoint()` - Assign to reception points
  - `applyRoutingRules()` - Apply custom routing rules

### 📊 Performance Monitoring (`/lib/`)
- **`realtime-monitor.ts`** - Performance monitoring utility
  - Connection quality tracking
  - Latency measurement
  - Error monitoring

- **`performance-tester.ts`** - Performance testing tools
  - Automated testing
  - Load testing
  - Latency analysis

- **`debug-utils.ts`** - Debug utilities
  - Connection debugging
  - Performance analysis
  - Network simulation

### ⚡ Rate Limiting (`/lib/`)
- **`rateLimit.ts`** - Rate limiting implementation
  - Message rate limiting
  - API protection
  - Abuse prevention

---

## 📝 TypeScript Types (`/types/index.ts`)

### Core Types
```typescript
interface ChatMessage {
  id: string
  chat_session_id: string
  sender_type: 'guest' | 'staff'
  content: string
  original_content?: string
  translated_content?: string
  is_translated: boolean
  created_at: string
}

interface ChatSession {
  id: string
  tenant_id: string
  guest_id?: string
  status: 'active' | 'ended' | 'pending'
  guest_language?: string
  auto_translate: boolean
  priority: string
}
```

---

## 🎨 Styling System

### Colors
- **Primary**: #f97316 (Orange)
- **Secondary**: #6b7280 (Gray)
- **Success**: #10b981 (Green)
- **Error**: #ef4444 (Red)

### Typography
- **Font Family**: Inter
- **Monospace**: JetBrains Mono

### Animations
- Smooth transitions (0.2s-0.3s)
- Micro-interactions
- Loading states
- Typing indicators

---

## 🔧 Configuration

### Environment Variables
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

### Next.js Config
- Transpile packages: @loaloa/ui, @loaloa/design-tokens
- Path aliases: @ → ./app
- SCSS support enabled

---

## 🚀 Key Features

### 💬 Real-time Chat
- WebSocket-powered messaging
- Typing indicators
- Message delivery status
- Auto-scroll to latest messages

### 🌐 Multi-language Support
- 12+ supported languages
- Automatic language detection
- Real-time translation với confidence scores
- Original text preservation

### 📱 QR Code Integration
- Instant access via QR scan
- No app download required
- Location-based message routing
- Multi-device support

### 🎯 Smart Routing
- Automatic staff assignment
- Department-based routing
- Priority message handling
- Workload balancing

---

## 🔍 Debug & Monitoring

### Enhanced Features
- Performance monitoring dashboard
- Connection quality indicators
- Real-time latency display
- Enhanced error handling
- Automated testing tools

### Debug Tools
- `/debug/realtime/` - Realtime debugging
- Performance testing utilities
- Network simulation tools
- Connection monitoring

---

## 📊 Performance Optimization

### Strategies
1. **Enhanced Debugging & Monitoring**
2. **Hybrid Optimization** (Realtime + Polling fallback)
3. **Performance Testing Tools**
4. **Connection quality monitoring**
5. **Intelligent fallback mechanisms**

---

## 🔗 API Endpoints Chi tiết

### Chat Sessions API
```typescript
// POST /api/chat-sessions
{
  temporary_user_id: string
  guest_language?: string
  source_type?: string
  source_qr_code_id?: string
  reception_point_id?: string
}

// Response
{
  success: boolean
  session: ChatSession
  routing_info: RoutingResult
  qr_info?: QRCodeInfo
}
```

### Messages API
```typescript
// GET /api/messages?session_id=xxx&limit=50&offset=0
// POST /api/messages
{
  session_id: string
  content: string
  sender_type: 'guest' | 'staff'
  sender_name?: string
}
```

### QR Scan API
```typescript
// GET /api/qr-scan/[code]
// Response
{
  success: boolean
  qr_code: QRCodeInfo
  temporary_user: TempUserInfo
  redirect_url: string
}
```

---

## 🛡️ Middleware & Security

### Next.js Middleware (`middleware.ts`)
- Route protection
- Authentication checks
- Request logging
- Rate limiting integration

### Security Features
- Password hashing với bcrypt (10 salt rounds)
- Session token generation
- CORS configuration
- Input validation
- SQL injection protection

---

## 📱 Pages & Routes

### Guest Routes
- `/` - Homepage
- `/chat/new` - New chat session
- `/chat/[session]` - Chat interface
- `/qr/demo` - QR demo page
- `/error` - Error handling

### Staff Routes
- `/staff` - Staff dashboard
- `/staff/dashboard` - Main dashboard
- `/staff/dashboard/enhanced` - Enhanced dashboard
- `/staff/auth/login` - Staff login

### Debug Routes
- `/debug/realtime` - Realtime debugging
- `/chat-enhanced/[session]` - Enhanced chat interface

---

## 🔄 Data Flow

### Message Flow
1. **Guest sends message** → `POST /api/messages`
2. **Message stored** in `tenant_chat_messages`
3. **Realtime broadcast** via Supabase
4. **Staff receives** via WebSocket subscription
5. **Translation** (if enabled)
6. **UI updates** in real-time

### Session Flow
1. **QR Code scan** → `GET /api/qr-scan/[code]`
2. **Temporary user creation**
3. **Session creation** → `POST /api/chat-sessions`
4. **Routing determination**
5. **Staff assignment**
6. **Chat interface** initialization

---

## 🧪 Testing & Development

### Development Commands
```bash
cd D:/loaloaapp/apps/web-chat
pnpm dev          # Development server
pnpm build        # Production build
pnpm start        # Production server
pnpm test         # Run tests
```

### Testing Tools
- Performance testing utilities
- Automated message testing
- Connection quality testing
- Load testing capabilities

### Debug Commands
```javascript
// Browser console
debugUtils.runQuickDebug()
debugUtils.testMessageLatency(sendFn)
debugUtils.monitorRealtimeConnection()
```

---

## 📦 Dependencies

### Core Dependencies
- `next` - Next.js framework
- `react` - React library
- `typescript` - TypeScript support
- `@supabase/supabase-js` - Supabase client
- `bcryptjs` - Password hashing
- `sass` - SCSS support

### UI Dependencies
- `@loaloa/ui` - Custom UI components
- `@loaloa/design-tokens` - Design system
- `@loaloa/license-client` - License management

---

## 🚨 Error Handling

### Error Types
- Connection errors
- Authentication errors
- Validation errors
- Rate limiting errors
- Translation errors

### Error Recovery
- Automatic reconnection
- Polling fallback
- Graceful degradation
- User-friendly error messages

---

## 📈 Performance Metrics

### Monitored Metrics
- Message latency
- Connection quality
- Subscription time
- Error rates
- Reconnection frequency

### Performance Targets
- Message delivery: < 1s
- Connection time: < 2s
- Translation time: < 3s
- UI responsiveness: < 100ms

---

*Cập nhật lần cuối: $(new Date().toISOString())*
