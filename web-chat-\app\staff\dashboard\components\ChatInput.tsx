'use client';

import { useState, useRef, useEffect } from 'react';
import styles from './ChatInput.module.scss';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  autoTranslate?: boolean;
  onToggleTranslation?: () => void;
}

export default function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message...",
  autoTranslate = true,
  onToggleTranslation
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      resetTextareaHeight();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    adjustTextareaHeight();
    
    // Typing indicator
    if (!isTyping) {
      setIsTyping(true);
      // TODO: Send typing start event
    }
    
    // Clear typing after delay
    setTimeout(() => {
      setIsTyping(false);
      // TODO: Send typing stop event
    }, 1000);
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  };

  const resetTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = '40px';
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  return (
    <form onSubmit={handleSubmit} className={styles.chatInputForm}>
      <div className={styles.inputContainer}>
        <div className={styles.textareaContainer}>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={disabled}
            className={styles.messageTextarea}
            rows={1}
          />
          
          <div className={styles.inputActions}>
            <button
              type="button"
              className={styles.emojiButton}
              title="Add emoji"
            >
              😊
            </button>
            
            <button
              type="button"
              className={styles.attachButton}
              title="Attach file"
            >
              📎
            </button>
          </div>
        </div>

        <div className={styles.sendContainer}>
          <button
            type="submit"
            disabled={!message.trim() || disabled}
            className={styles.sendButton}
            title="Send message"
          >
            <span className={styles.sendIcon}>➤</span>
          </button>
        </div>
      </div>

      <div className={styles.inputFooter}>
        <div className={styles.translationToggle}>
          <label className={styles.toggleLabel}>
            <input
              type="checkbox"
              checked={autoTranslate}
              onChange={onToggleTranslation}
              className={styles.toggleCheckbox}
            />
            <span className={styles.toggleSlider}></span>
            <span className={styles.toggleText}>Auto Translate</span>
          </label>
        </div>

        <div className={styles.quickActions}>
          <button type="button" className={styles.quickAction}>
            🏨 Room Service
          </button>
          <button type="button" className={styles.quickAction}>
            🧹 Housekeeping
          </button>
          <button type="button" className={styles.quickAction}>
            🍽️ Restaurant
          </button>
        </div>
      </div>
    </form>
  );
}
