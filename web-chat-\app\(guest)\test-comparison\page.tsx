'use client';

/**
 * Quick Comparison Test Page
 * Compare original vs enhanced implementations side by side
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import debugUtils from '@/lib/debug-utils';

export default function TestComparisonPage() {
  const router = useRouter();
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTest, setIsRunningTest] = useState(false);

  useEffect(() => {
    debugUtils.runQuickDebug();
  }, []);

  const runPerformanceTest = async () => {
    setIsRunningTest(true);
    console.log('🧪 Starting performance comparison test...');

    try {
      // Test polling intervals
      const intervals = debugUtils.comparePollingIntervals();
      
      // Simulate message latency test
      const mockSendMessage = async (content: string) => {
        const startTime = Date.now();
        await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500)); // 0.5-2.5s delay
        return Date.now() - startTime;
      };

      const testLatencies = [];
      for (let i = 0; i < 5; i++) {
        const latency = await mockSendMessage(`Test message ${i + 1}`);
        testLatencies.push(latency);
        console.log(`Test ${i + 1}: ${latency}ms`);
      }

      const avgLatency = testLatencies.reduce((sum, lat) => sum + lat, 0) / testLatencies.length;

      setTestResults({
        intervals,
        avgLatency: Math.round(avgLatency),
        maxLatency: Math.max(...testLatencies),
        minLatency: Math.min(...testLatencies),
        testCount: testLatencies.length
      });

    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setIsRunningTest(false);
    }
  };

  const navigateToOriginal = () => {
    router.push('/staff/dashboard');
  };

  const navigateToEnhanced = () => {
    router.push('/staff/dashboard/enhanced');
  };

  const navigateToSimpleEnhanced = () => {
    router.push('/staff/dashboard/simple-enhanced');
  };

  const navigateToPollingOnly = () => {
    router.push('/staff/dashboard/polling-only');
  };

  const navigateToGuestOriginal = () => {
    const sessionId = 'test-session-' + Date.now();
    router.push(`/chat/${sessionId}`);
  };

  const navigateToGuestEnhanced = () => {
    const sessionId = 'test-session-' + Date.now();
    router.push(`/chat-enhanced/${sessionId}`);
  };

  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧪 Enhanced Chat Performance Test</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>📊 Quick Comparison</h2>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
          <div style={{ padding: '1rem', border: '1px solid #ddd', borderRadius: '8px' }}>
            <h3>❌ Original Implementation</h3>
            <ul>
              <li>Polling interval: 5-10 seconds</li>
              <li>No performance monitoring</li>
              <li>Basic error handling</li>
              <li>Fixed refresh rates</li>
            </ul>
            <div style={{ marginTop: '1rem' }}>
              <button 
                onClick={navigateToOriginal}
                style={{ 
                  padding: '0.5rem 1rem', 
                  marginRight: '0.5rem',
                  background: '#ef4444', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Staff Original
              </button>
              <button 
                onClick={navigateToGuestOriginal}
                style={{ 
                  padding: '0.5rem 1rem', 
                  background: '#f97316', 
                  color: 'white', 
                  border: 'none', 
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Guest Original
              </button>
            </div>
          </div>
          
          <div style={{ padding: '1rem', border: '1px solid #f59e0b', borderRadius: '8px' }}>
            <h3>⚡ Polling Only</h3>
            <ul>
              <li>Pure polling: 0.5-5 seconds</li>
              <li>No realtime complexity</li>
              <li>Adjustable intervals</li>
              <li>Guaranteed to work</li>
            </ul>
            <div style={{ marginTop: '1rem' }}>
              <button
                onClick={navigateToPollingOnly}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#f59e0b',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Polling Only
              </button>
            </div>
          </div>

          <div style={{ padding: '1rem', border: '1px solid #3b82f6', borderRadius: '8px' }}>
            <h3>🔧 Simple Enhanced</h3>
            <ul>
              <li>Polling interval: 1.5 seconds (fixed)</li>
              <li>Quick realtime fallback</li>
              <li>Minimal monitoring</li>
              <li>Stable implementation</li>
            </ul>
            <div style={{ marginTop: '1rem' }}>
              <button
                onClick={navigateToSimpleEnhanced}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Simple Enhanced
              </button>
            </div>
          </div>

          <div style={{ padding: '1rem', border: '1px solid #10b981', borderRadius: '8px' }}>
            <h3>✅ Full Enhanced</h3>
            <ul>
              <li>Polling interval: 1-4 seconds (adaptive)</li>
              <li>Real-time performance monitoring</li>
              <li>Advanced error tracking</li>
              <li>Dynamic optimization</li>
            </ul>
            <div style={{ marginTop: '1rem' }}>
              <button
                onClick={navigateToEnhanced}
                style={{
                  padding: '0.5rem 1rem',
                  marginRight: '0.5rem',
                  background: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Full Enhanced
              </button>
              <button
                onClick={navigateToGuestEnhanced}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Test Guest Enhanced
              </button>
            </div>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>🔧 Debug Tools</h2>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <button 
            onClick={() => debugUtils.forcePollingMode()}
            style={{ 
              padding: '0.5rem 1rem', 
              background: '#6b7280', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Force Polling Mode
          </button>
          
          <button 
            onClick={() => debugUtils.monitorRealtimeConnection()}
            style={{ 
              padding: '0.5rem 1rem', 
              background: '#8b5cf6', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Monitor Connection
          </button>
          
          <button 
            onClick={() => debugUtils.simulateNetworkDelay(3000)}
            style={{ 
              padding: '0.5rem 1rem', 
              background: '#f59e0b', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Simulate 3s Delay
          </button>
          
          <button 
            onClick={runPerformanceTest}
            disabled={isRunningTest}
            style={{ 
              padding: '0.5rem 1rem', 
              background: isRunningTest ? '#9ca3af' : '#dc2626', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: isRunningTest ? 'not-allowed' : 'pointer'
            }}
          >
            {isRunningTest ? 'Running Test...' : 'Run Performance Test'}
          </button>
        </div>
      </div>

      {testResults && (
        <div style={{ marginBottom: '2rem' }}>
          <h2>📊 Test Results</h2>
          <div style={{ padding: '1rem', background: '#f3f4f6', borderRadius: '8px' }}>
            <h3>Polling Intervals</h3>
            <p>Original: {testResults.intervals.original}ms</p>
            <p>Enhanced: {testResults.intervals.enhanced}ms</p>
            <p>Enhanced Max: {testResults.intervals.adaptiveMax}ms</p>
            
            <h3>Simulated Latency</h3>
            <p>Average: {testResults.avgLatency}ms</p>
            <p>Max: {testResults.maxLatency}ms</p>
            <p>Min: {testResults.minLatency}ms</p>
            <p>Tests: {testResults.testCount}</p>
          </div>
        </div>
      )}

      <div style={{ marginBottom: '2rem' }}>
        <h2>📝 Testing Instructions</h2>
        <ol>
          <li><strong>Test Original:</strong> Click "Test Staff Original" or "Test Guest Original"</li>
          <li><strong>Send messages:</strong> Send a few test messages and note the delay</li>
          <li><strong>Test Enhanced:</strong> Click "Test Staff Enhanced" or "Test Guest Enhanced"</li>
          <li><strong>Compare:</strong> Send the same messages and compare the delay</li>
          <li><strong>Check Console:</strong> Open browser DevTools to see detailed logs</li>
        </ol>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>🔍 What to Look For</h2>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
          <div>
            <h3>Enhanced Features</h3>
            <ul>
              <li>Connection status indicators</li>
              <li>Performance metrics panel</li>
              <li>Faster message delivery</li>
              <li>Better error handling</li>
              <li>Adaptive polling intervals</li>
            </ul>
          </div>
          
          <div>
            <h3>Console Logs</h3>
            <ul>
              <li>🚀 Enhanced initialization logs</li>
              <li>📊 Performance monitoring</li>
              <li>🔔 Realtime message events</li>
              <li>📥 Polling fallback logs</li>
              <li>⚖️ Comparison results</li>
            </ul>
          </div>
        </div>
      </div>

      <div style={{ padding: '1rem', background: '#fef3e2', borderRadius: '8px', border: '1px solid #f97316' }}>
        <h3>💡 Expected Results</h3>
        <p><strong>Enhanced version should show:</strong></p>
        <ul>
          <li>Messages appearing in 1-2 seconds instead of 5-6 seconds</li>
          <li>Visual connection status indicators</li>
          <li>Performance metrics in the UI</li>
          <li>More detailed console logging</li>
          <li>Better error handling and recovery</li>
        </ul>
      </div>
    </div>
  );
}
