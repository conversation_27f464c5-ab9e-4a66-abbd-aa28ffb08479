// Scan Page Styles
$primary-color: #f97316;
$primary-dark: #ea580c;
$primary-light: #fed7aa;
$success-color: #10b981;
$error-color: #ef4444;
$text-color: #111827;
$text-light: #6b7280;
$text-muted: #9ca3af;
$background: #ffffff;
$surface: #f8fafc;
$border: #e5e7eb;
$radius: 0.75rem;
$radius-lg: 1rem;
$shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
$transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, $surface 0%, $background 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.scanCard {
  background: $background;
  border-radius: $radius-lg;
  padding: 2rem;
  box-shadow: $shadow-lg;
  max-width: 500px;
  width: 100%;
  border: 1px solid $border;
}

// Header
.header {
  text-align: center;
  margin-bottom: 2rem;
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    
    .logoIcon {
      font-size: 2.5rem;
      filter: drop-shadow(0 2px 4px rgba(249, 115, 22, 0.3));
    }
    
    .logoText {
      font-size: 2rem;
      font-weight: 700;
      background: linear-gradient(135deg, $primary-color, $primary-dark);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin: 0;
    }
  }
  
  .subtitle {
    color: $text-light;
    font-size: 1.1rem;
    margin: 0;
  }
}

// Language Section
.languageSection {
  margin-bottom: 2rem;
  
  .languageLabel {
    display: block;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
  }
}

// QR Form
.qrForm {
  margin-bottom: 2rem;
  
  .inputGroup {
    margin-bottom: 1rem;
    
    .inputLabel {
      display: block;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }
    
    .qrInput {
      width: 100%;
      padding: 1rem;
      border: 2px solid $border;
      border-radius: $radius;
      font-size: 1rem;
      transition: $transition;
      background: $background;
      
      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1);
      }
      
      &:disabled {
        background: $surface;
        cursor: not-allowed;
        opacity: 0.6;
      }
      
      &::placeholder {
        color: $text-muted;
      }
    }
  }
  
  .errorMessage {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: $error-color;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: $radius;
    border: 1px solid rgba(239, 68, 68, 0.2);
    
    .errorIcon {
      font-size: 1rem;
    }
  }
  
  .submitButton {
    width: 100%;
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: $radius;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: $transition;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
    
    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $primary-dark, #dc2626);
      box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .spinner {
      width: 1rem;
      height: 1rem;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .buttonIcon {
      font-size: 1.1rem;
    }
  }
}

// Quick Actions
.quickActions {
  margin-bottom: 2rem;
  
  .quickTitle {
    font-size: 1rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 1rem;
  }
  
  .actionButtons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }
  
  .actionButton {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: $radius;
    border: 2px solid $border;
    background: $background;
    cursor: pointer;
    transition: $transition;
    text-align: left;
    
    &:hover:not(:disabled) {
      border-color: $primary-color;
      box-shadow: $shadow;
      transform: translateY(-1px);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .actionIcon {
      font-size: 1.5rem;
      flex-shrink: 0;
    }
    
    .actionContent {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      
      .actionTitle {
        font-weight: 600;
        color: $text-color;
        font-size: 0.875rem;
      }
      
      .actionSubtitle {
        color: $text-light;
        font-size: 0.75rem;
      }
    }
    
    &.demoButton {
      border-color: rgba(16, 185, 129, 0.3);
      
      &:hover:not(:disabled) {
        border-color: $success-color;
        background: rgba(16, 185, 129, 0.05);
      }
    }
    
    &.businessButton {
      border-color: rgba(59, 130, 246, 0.3);
      
      &:hover:not(:disabled) {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.05);
      }
    }
  }
}

// Instructions
.instructions {
  margin-bottom: 2rem;
  
  .instructionsTitle {
    font-size: 0.9rem;
    font-weight: 600;
    color: $text-color;
    margin-bottom: 0.75rem;
  }
  
  .instructionsList {
    color: $text-light;
    font-size: 0.875rem;
    line-height: 1.6;
    padding-left: 1.25rem;
    margin: 0;
    
    li {
      margin-bottom: 0.25rem;
    }
  }
}

// Features
.features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 2rem;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
  
  .feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: $surface;
    border-radius: $radius;
    border: 1px solid $border;
    
    .featureIcon {
      font-size: 1rem;
      flex-shrink: 0;
    }
    
    .featureText {
      font-size: 0.8rem;
      color: $text-light;
      font-weight: 500;
    }
  }
}

// Footer
.footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid $border;
  
  .footerText {
    color: $text-muted;
    font-size: 0.8rem;
    margin: 0;
  }
}

// Animations
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive Design
@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .scanCard {
    padding: 1.5rem;
  }
  
  .header .logo {
    flex-direction: column;
    gap: 0.5rem;
    
    .logoIcon {
      font-size: 2rem;
    }
    
    .logoText {
      font-size: 1.5rem;
    }
  }
}
