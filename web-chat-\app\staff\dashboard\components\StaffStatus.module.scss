.staffStatus {
  position: relative;
  display: inline-block;
}

.statusButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  
  &:hover {
    background: #f9fafb;
    border-color: #d1d5db;
  }
  
  .statusIcon {
    font-size: 12px;
  }
  
  .statusLabel {
    font-weight: 500;
    color: #374151;
  }
  
  .dropdownArrow {
    font-size: 10px;
    color: #9ca3af;
    transition: transform 0.2s;
  }
  
  &:hover .dropdownArrow {
    transform: rotate(180deg);
  }
}

.statusDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.2s ease-out;
  
  .statusHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafafa;
    
    h4 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #111827;
    }
    
    .closeButton {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 16px;
      color: #6b7280;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s;
      
      &:hover {
        background: #f3f4f6;
        color: #374151;
      }
    }
  }
  
  .statusOptions {
    padding: 12px;
    
    .statusOption {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
      padding: 12px;
      background: none;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 4px;
      
      &:hover {
        background: #f8fafc;
      }
      
      &.selected {
        background: #fef3e2;
        border: 1px solid #fed7aa;
      }
      
      .optionIcon {
        font-size: 16px;
        flex-shrink: 0;
      }
      
      .optionContent {
        flex: 1;
        text-align: left;
        
        .optionLabel {
          display: block;
          font-weight: 500;
          color: #111827;
          font-size: 14px;
          margin-bottom: 2px;
        }
        
        .optionDescription {
          display: block;
          font-size: 12px;
          color: #6b7280;
        }
      }
      
      .checkIcon {
        color: #f97316;
        font-weight: 600;
        flex-shrink: 0;
      }
    }
  }
  
  .workingHours {
    padding: 16px 20px;
    border-top: 1px solid #f3f4f6;
    background: #fafafa;
    
    h5 {
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 12px 0;
      color: #374151;
    }
    
    .timeInputs {
      display: flex;
      gap: 16px;
      
      .timeInput {
        flex: 1;
        
        label {
          display: block;
          font-size: 12px;
          font-weight: 500;
          color: #6b7280;
          margin-bottom: 4px;
        }
        
        input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          
          &:focus {
            outline: none;
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
          }
        }
      }
    }
  }
  
  .statusFooter {
    padding: 12px 20px;
    border-top: 1px solid #f3f4f6;
    
    .statusNote {
      font-size: 11px;
      color: #9ca3af;
      margin: 0;
      text-align: center;
      font-style: italic;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
