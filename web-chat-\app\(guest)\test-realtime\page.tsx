'use client'

import { useState, useEffect } from 'react'

interface RealtimeStatus {
  success: boolean
  realtime_ready: boolean
  tables_status: Array<{
    table: string
    realtime_enabled: boolean
  }>
  all_enabled_tables: string[]
  missing_tables: string[]
  recommendations: string
}

export default function TestRealtimePage() {
  const [status, setStatus] = useState<RealtimeStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [setupResult, setSetupResult] = useState<any>(null)

  const checkRealtimeStatus = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/realtime/setup')
      const data = await response.json()

      if (response.ok) {
        setStatus(data)
      } else {
        setError(data.error || 'Failed to check realtime status')
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error')
    } finally {
      setLoading(false)
    }
  }

  const setupRealtime = async () => {
    try {
      setLoading(true)
      setError(null)
      setSetupResult(null)

      const response = await fetch('/api/realtime/setup', {
        method: 'POST'
      })
      const data = await response.json()

      setSetupResult(data)
      
      // Refresh status after setup
      setTimeout(() => {
        checkRealtimeStatus()
      }, 1000)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkRealtimeStatus()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Supabase Realtime Setup & Test</h1>
        
        {/* Status Check */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Realtime Status</h2>
            <button
              onClick={checkRealtimeStatus}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Checking...' : 'Refresh Status'}
            </button>
          </div>

          {status && (
            <div className="space-y-4">
              <div className={`p-4 rounded ${status.realtime_ready ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                <div className="font-semibold">
                  {status.realtime_ready ? '✅ Realtime Ready' : '⚠️ Setup Required'}
                </div>
                <div className="text-sm mt-1">{status.recommendations}</div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Tables Status:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {status.tables_status.map(table => (
                    <div
                      key={table.table}
                      className={`p-3 rounded border ${
                        table.realtime_enabled 
                          ? 'bg-green-50 border-green-200' 
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="font-medium">{table.table}</div>
                      <div className={`text-sm ${
                        table.realtime_enabled ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {table.realtime_enabled ? '✅ Enabled' : '❌ Disabled'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {status.missing_tables.length > 0 && (
                <div className="bg-red-50 border border-red-200 p-4 rounded">
                  <h4 className="font-semibold text-red-800 mb-2">Missing Tables:</h4>
                  <ul className="text-red-700 text-sm">
                    {status.missing_tables.map(table => (
                      <li key={table}>• {table}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Setup Button */}
        {status && !status.realtime_ready && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4">Setup Realtime</h2>
            <p className="text-gray-600 mb-4">
              Click the button below to automatically enable realtime for chat tables.
            </p>
            <button
              onClick={setupRealtime}
              disabled={loading}
              className="bg-orange-500 text-white px-6 py-3 rounded hover:bg-orange-600 disabled:opacity-50"
            >
              {loading ? 'Setting up...' : 'Enable Realtime'}
            </button>
          </div>
        )}

        {/* Setup Results */}
        {setupResult && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4">Setup Results</h2>
            
            {setupResult.success ? (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                ✅ {setupResult.message}
              </div>
            ) : (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                ❌ {setupResult.error}
              </div>
            )}

            {setupResult.results && (
              <div className="space-y-2">
                <h3 className="font-semibold">Table Results:</h3>
                {setupResult.results.map((result: any, index: number) => (
                  <div
                    key={index}
                    className={`p-2 rounded text-sm ${
                      result.realtime_enabled 
                        ? 'bg-green-50 text-green-700' 
                        : 'bg-red-50 text-red-700'
                    }`}
                  >
                    <strong>{result.table}:</strong> {
                      result.realtime_enabled ? 'Enabled' : `Failed - ${result.error}`
                    }
                  </div>
                ))}
              </div>
            )}

            {setupResult.setup_instructions && (
              <div className="mt-4">
                <h3 className="font-semibold mb-2">Manual Setup (if needed):</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                  {setupResult.setup_instructions.manual_sql}
                </pre>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Instructions</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <div>
              <strong>1. Check Status:</strong> Click "Refresh Status" to see current realtime configuration.
            </div>
            <div>
              <strong>2. Enable Realtime:</strong> If not ready, click "Enable Realtime" to automatically configure.
            </div>
            <div>
              <strong>3. Manual Setup:</strong> If automatic setup fails, run the provided SQL commands in Supabase SQL Editor.
            </div>
            <div>
              <strong>4. Test Chat:</strong> Once realtime is enabled, test the chat system for instant messaging.
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
