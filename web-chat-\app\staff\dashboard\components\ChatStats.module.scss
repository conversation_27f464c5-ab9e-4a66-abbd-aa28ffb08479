.chatStats {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.statsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
  
  .toggleButton {
    background: #f3f4f6;
    border: none;
    border-radius: 6px;
    padding: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    
    &:hover {
      background: #e5e7eb;
      transform: scale(1.1);
    }
  }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  
  .statCard {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #f3f4f6;
    transition: all 0.2s;
    
    &:hover {
      background: #f3f4f6;
      transform: translateY(-1px);
    }
    
    .statIcon {
      font-size: 20px;
      flex-shrink: 0;
    }
    
    .statContent {
      display: flex;
      flex-direction: column;
      min-width: 0;
      
      .statValue {
        font-size: 18px;
        font-weight: 700;
        color: #f97316;
        line-height: 1;
      }
      
      .statLabel {
        font-size: 11px;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
      }
    }
  }
}

.detailedStats {
  border-top: 1px solid #f3f4f6;
  padding-top: 16px;
  animation: slideDown 0.3s ease-out;
  
  .statsRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .statDetail {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      
      .detailLabel {
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 4px;
      }
      
      .detailValue {
        font-size: 16px;
        font-weight: 600;
        color: #111827;
      }
    }
  }
  
  .statusBreakdown {
    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 12px 0;
    }
    
    .progressBars {
      .progressItem {
        margin-bottom: 8px;
        
        .progressLabel {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 4px;
          display: block;
        }
        
        .progressBar {
          height: 6px;
          background: #f3f4f6;
          border-radius: 3px;
          overflow: hidden;
          
          .progressFill {
            height: 100%;
            transition: width 0.5s ease-out;
            border-radius: 3px;
          }
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .statsRow {
    flex-direction: column;
    gap: 12px;
  }
}
