# 🎣 Hooks & Utilities Index - LoaLoa Web Chat

## 📋 Tổng quan

Danh sách đầy đủ tất cả custom hooks, utilities, services và helper functions trong ứng dụng web-chat.

---

## 🎣 Custom Hooks

### `useChat.ts`
**Location**: `app/hooks/useChat.ts`

**Purpose**: Standard chat hook cho real-time messaging

**Parameters**:
```typescript
interface UseChatOptions {
  sessionId: string
  guestId: string
  autoTranslate?: boolean
  guestLanguage?: string
}
```

**Returns**:
```typescript
interface UseChatReturn {
  messages: ChatMessage[]
  session: ChatSession | null
  loading: boolean
  connected: boolean
  isTyping: boolean
  error: string | null
  sendMessage: (content: string) => Promise<boolean>
  startTyping: () => void
  stopTyping: () => void
}
```

**Key Features**:
- ✅ Supabase Realtime integration
- ✅ Message state management
- ✅ Connection status tracking
- ✅ Typing indicators
- ✅ Error handling
- ✅ Auto cleanup on unmount

### `useChat.enhanced.ts`
**Location**: `app/hooks/useChat.enhanced.ts`

**Purpose**: Enhanced version với performance monitoring

**Additional Features**:
- ✅ Performance tracking
- ✅ Connection quality monitoring
- ✅ Latency measurement
- ✅ Fallback mechanisms
- ✅ Debug capabilities
- ✅ Real-time metrics

**Usage**:
```typescript
const {
  messages,
  session,
  loading,
  connected,
  realtimeConnected,
  usePollingFallback,
  sendMessage,
  performanceMetrics
} = useChatEnhanced({
  sessionId,
  guestId,
  guestLanguage: 'en',
  autoTranslate: true
})
```

### `useChat.realtime.ts`
**Location**: `app/hooks/useChat.realtime.ts`

**Purpose**: Realtime-focused implementation

**Specialized Features**:
- ✅ WebSocket optimization
- ✅ Connection recovery
- ✅ Polling fallback
- ✅ Network resilience
- ✅ Subscription management

---

## 🛠️ Utility Functions

### Supabase Utilities (`/lib/supabase/`)

#### `index.ts` - Main Supabase Factory
```typescript
// Client-side Supabase client
export const createClientSupabase = () => {
  return createClient(supabaseUrl, supabaseAnonKey)
}

// Server-side Supabase client
export const createServerSupabase = () => {
  return createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}
```

#### `client.ts` - Client-side Utilities
- Browser-specific Supabase client
- Real-time subscriptions
- Client-side authentication

#### `server.ts` - Server-side Utilities
- Server-side Supabase client
- API route integration
- Database operations

#### `admin.ts` - Admin Utilities
- Service role client
- Administrative operations
- Elevated permissions

---

## 🔐 Authentication Utilities (`/lib/auth/`)

### `password.ts`
**Functions**:
```typescript
// Hash password với bcrypt
export async function hashPassword(password: string): Promise<string>

// Verify password
export async function verifyPassword(
  password: string, 
  hashedPassword: string
): Promise<boolean>

// Generate session token
export function generateSessionToken(): string

// Validate session token
export function validateSessionToken(token: string): boolean
```

**Features**:
- ✅ bcrypt hashing (10 salt rounds)
- ✅ Secure token generation
- ✅ Session management
- ✅ Password validation

---

## 🎯 Service Layer (`/lib/services/`)

### `chatRouting.ts`
**Purpose**: Smart message routing service

**Key Functions**:
```typescript
// Determine routing based on context
export async function determineRouting(
  context: RoutingContext
): Promise<RoutingResult>

// Apply routing rules
export async function applyRoutingRules(
  qrCode: QRCodeInfo,
  tenantId: string
): Promise<RoutingRule[]>

// Assign to reception point
export async function assignToReceptionPoint(
  sessionId: string,
  receptionPointId: string
): Promise<boolean>
```

**Routing Logic**:
- QR code-based routing
- Department assignment
- Staff workload balancing
- Priority handling
- Custom routing rules

---

## 📊 Performance Utilities (`/lib/`)

### `realtime-monitor.ts`
**Purpose**: Performance monitoring và tracking

**Key Features**:
```typescript
class RealtimeMonitor {
  // Start connection monitoring
  startConnectionMonitoring(): void
  
  // Record message latency
  recordMessageLatency(trackingId: string, latency: number): void
  
  // Get performance summary
  getPerformanceSummary(): PerformanceSummary
  
  // Log performance data
  logPerformanceSummary(): void
}
```

**Metrics Tracked**:
- Connection time
- Message latency
- Subscription time
- Error rates
- Reconnection frequency

### `performance-tester.ts`
**Purpose**: Automated performance testing

**Functions**:
```typescript
// Run automated test
export async function runAutomatedTest(
  sendMessageFn: Function,
  options: TestOptions
): Promise<TestResults>

// Measure latency
export function measureLatency(
  startTime: number,
  endTime: number
): number

// Generate test messages
export function generateTestMessages(count: number): string[]
```

### `debug-utils.ts`
**Purpose**: Development và debugging tools

**Available Functions**:
```typescript
// Force polling mode
export function forcePollingMode(): void

// Test message latency
export function testMessageLatency(sendFn: Function): Promise<number>

// Monitor realtime connection
export function monitorRealtimeConnection(): void

// Simulate network delay
export function simulateNetworkDelay(ms: number): void

// Run quick debug check
export function runQuickDebug(): void
```

**Global Debug Object**:
```javascript
// Available in browser console
window.debugUtils = {
  forcePollingMode,
  testMessageLatency,
  monitorRealtimeConnection,
  simulateNetworkDelay,
  runQuickDebug
}
```

---

## ⚡ Rate Limiting (`/lib/rateLimit.ts`)

**Purpose**: API protection và abuse prevention

**Features**:
- Message rate limiting
- IP-based limiting
- Sliding window algorithm
- Configurable limits
- Redis integration (optional)

**Usage**:
```typescript
// Check rate limit
const isAllowed = await checkRateLimit(
  identifier: string,
  limit: number,
  windowMs: number
)

// Apply rate limit to API route
export function withRateLimit(handler: Function, options: RateLimitOptions)
```

---

## 🔧 Helper Functions

### Message Utilities
```typescript
// Generate tracking ID
export function generateTrackingId(): string

// Extract message ID
export function extractMessageId(message: any): string

// Format timestamp
export function formatTimestamp(date: Date): string

// Validate message content
export function validateMessageContent(content: string): boolean
```

### Language Utilities
```typescript
// Detect language
export function detectLanguage(text: string): Promise<string>

// Get language info
export function getLanguageInfo(code: string): LanguageInfo

// Format language name
export function formatLanguageName(code: string, native?: boolean): string
```

### QR Code Utilities
```typescript
// Generate guest display name
export function generateGuestDisplayName(qrCode: QRCodeInfo): string

// Validate QR code format
export function validateQRCode(code: string): boolean

// Parse QR code data
export function parseQRCodeData(code: string): QRCodeData
```

---

## 🧪 Testing Utilities

### Test Helpers
```typescript
// Mock Supabase client
export function createMockSupabaseClient(): MockSupabaseClient

// Generate test data
export function generateTestChatSession(): ChatSession
export function generateTestMessage(): ChatMessage

// Setup test environment
export function setupTestEnvironment(): TestEnvironment
```

### Performance Testing
```typescript
// Load testing
export async function runLoadTest(
  concurrent: number,
  duration: number
): Promise<LoadTestResults>

// Stress testing
export async function runStressTest(
  maxLoad: number
): Promise<StressTestResults>
```

---

## 📈 Monitoring & Analytics

### Metrics Collection
- Real-time performance metrics
- Error tracking
- User behavior analytics
- System health monitoring

### Logging
- Structured logging
- Error reporting
- Performance logging
- Debug logging

---

## 🔄 Integration Patterns

### Hook Composition
```typescript
// Combining multiple hooks
function useChatWithTranslation(options: ChatOptions) {
  const chat = useChat(options)
  const translation = useTranslation(options.guestLanguage)
  
  return {
    ...chat,
    ...translation
  }
}
```

### Service Integration
```typescript
// Using services in hooks
function useEnhancedChat(options: ChatOptions) {
  const routing = useMemo(() => 
    chatRoutingService.determineRouting(context), [context]
  )
  
  const monitor = useRef(new RealtimeMonitor())
  
  // ... hook logic
}
```

---

*Cập nhật lần cuối: $(new Date().toISOString())*
