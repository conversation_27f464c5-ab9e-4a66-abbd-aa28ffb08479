# 🧩 Components Index - LoaLoa Web Chat

## 📋 Tổng quan Components

<PERSON>h sách đầy đủ tất cả React components trong ứng dụng web-chat với mô tả chi tiết về props, features và cách sử dụng.

---

## 💬 Chat Components

### `ChatInterface.tsx`
**Location**: `app/components/chat/ChatInterface.tsx`

**Purpose**: Main chat interface component cho guest và staff

**Props**:
```typescript
interface ChatInterfaceProps {
  sessionId: string
  guestId: string
  guestLanguage: string
  messages: ChatMessage[]
  session: ChatSession | null
  loading: boolean
  connected: boolean
  onSendMessage: (content: string) => Promise<boolean>
  onStartTyping: () => void
  onStopTyping: () => void
  isTyping: boolean
  error: string | null
  realtimeConnected?: boolean
  usePollingFallback?: boolean
}
```

**Key Features**:
- ✅ Auto-scroll to latest messages
- ✅ Typing indicators
- ✅ Message status indicators
- ✅ Translation toggle
- ✅ Connection status display
- ✅ Error handling
- ✅ Responsive design

**Styling**: `ChatInterface.improved.module.scss`

**Usage**:
```tsx
<ChatInterface
  sessionId={sessionId}
  guestId={guestId}
  guestLanguage="en"
  messages={messages}
  session={session}
  loading={loading}
  connected={connected}
  onSendMessage={handleSendMessage}
  onStartTyping={handleStartTyping}
  onStopTyping={handleStopTyping}
  isTyping={isTyping}
  error={error}
/>
```

---

## 🌐 Language Components

### `LanguageSelector.tsx`
**Location**: `app/components/language/LanguageSelector.tsx`

**Purpose**: Dropdown component for language selection

**Props**:
```typescript
interface LanguageSelectorProps {
  currentLanguage: string
  onLanguageChange: (languageCode: string) => void
  availableLanguages?: Language[]
  showNativeName?: boolean
  compact?: boolean
  disabled?: boolean
  className?: string
}
```

**Key Features**:
- ✅ 12+ supported languages
- ✅ Search functionality
- ✅ Native language names
- ✅ Flag icons
- ✅ RTL support
- ✅ Keyboard navigation
- ✅ Compact mode

**Supported Languages**:
- English (EN), Vietnamese (VI), Korean (KO)
- Japanese (JA), Chinese (ZH), Thai (TH)
- Indonesian (ID), Malay (MS), Spanish (ES)
- French (FR), German (DE), Arabic (AR)

**Styling**: `LanguageSelector.module.scss`

### `TranslationToggle.tsx`
**Location**: `app/components/language/TranslationToggle.tsx`

**Purpose**: Toggle switch for enabling/disabling translation

**Props**:
```typescript
interface TranslationToggleProps {
  enabled: boolean
  onToggle: (enabled: boolean) => void
  showLabels?: boolean
  disabled?: boolean
  className?: string
}
```

**Key Features**:
- ✅ Smooth toggle animation
- ✅ Visual feedback
- ✅ Accessibility support
- ✅ Custom styling

**Styling**: `TranslationToggle.module.scss`

---

## 👥 Staff Components

### Staff Dashboard Components
**Location**: `app/staff/` directory

**Components**:
- `Dashboard.tsx` - Main staff dashboard
- `SessionList.tsx` - List of active sessions
- `MessagePanel.tsx` - Message management panel
- `StaffLogin.tsx` - Staff authentication

**Features**:
- ✅ Real-time session monitoring
- ✅ Message routing management
- ✅ Performance metrics
- ✅ Staff assignment tools

---

## 🔧 Enhanced Components

### Enhanced Chat Interface
**Location**: `app/(guest)/chat-enhanced/[session]/page.tsx`

**Purpose**: Enhanced version với performance monitoring

**Additional Features**:
- ✅ Performance panel
- ✅ Connection quality indicator
- ✅ Real-time latency display
- ✅ Debug information
- ✅ Network status monitoring

### Enhanced Staff Dashboard
**Location**: `app/staff/dashboard/enhanced/page.tsx`

**Purpose**: Advanced staff dashboard với monitoring tools

**Additional Features**:
- ✅ Performance metrics dashboard
- ✅ Real-time connection monitoring
- ✅ Message latency tracking
- ✅ Error rate monitoring
- ✅ System health indicators

---

## 🎨 Styling Architecture

### SCSS Modules Structure
```
components/
├── chat/
│   ├── ChatInterface.module.scss
│   ├── ChatInterface.improved.module.scss
│   └── ChatInterface.enhanced.module.scss
├── language/
│   ├── LanguageSelector.module.scss
│   └── TranslationToggle.module.scss
└── staff/
    └── [staff-specific styles]
```

### Design Tokens
- **Colors**: Primary (#f97316), Secondary (#6b7280)
- **Spacing**: 4px base unit system
- **Typography**: Inter font family
- **Animations**: 0.2s-0.3s transitions

---

## 🔄 Component Interactions

### Data Flow
```
Parent Component
    ↓ props
ChatInterface
    ↓ events
useChat Hook
    ↓ API calls
Supabase Client
    ↓ realtime
WebSocket Connection
```

### Event Handling
- `onSendMessage` - Message sending
- `onLanguageChange` - Language switching
- `onToggleTranslation` - Translation toggle
- `onStartTyping` / `onStopTyping` - Typing indicators

---

## 🧪 Component Testing

### Testing Strategy
- Unit tests for individual components
- Integration tests for component interactions
- E2E tests for complete user flows
- Performance tests for real-time features

### Test Files Structure
```
__tests__/
├── components/
│   ├── ChatInterface.test.tsx
│   ├── LanguageSelector.test.tsx
│   └── TranslationToggle.test.tsx
└── integration/
    └── chat-flow.test.tsx
```

---

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Mobile Optimizations
- Touch-friendly interface
- Optimized keyboard handling
- Reduced animation complexity
- Compressed image assets

---

## ♿ Accessibility Features

### WCAG Compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management
- ARIA labels and roles

### Accessibility Props
```typescript
// Example accessibility props
{
  'aria-label': 'Send message',
  'aria-describedby': 'message-help',
  'role': 'button',
  'tabIndex': 0
}
```

---

## 🔧 Development Guidelines

### Component Creation Checklist
- [ ] TypeScript interfaces defined
- [ ] Props validation
- [ ] Error boundaries
- [ ] Loading states
- [ ] Accessibility features
- [ ] Responsive design
- [ ] SCSS modules
- [ ] Unit tests
- [ ] Documentation

### Best Practices
- Use functional components với hooks
- Implement proper error handling
- Follow naming conventions
- Optimize for performance
- Maintain accessibility standards

---

*Cập nhật lần cuối: $(new Date().toISOString())*
