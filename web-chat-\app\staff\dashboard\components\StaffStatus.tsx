'use client';

import { useState, useEffect } from 'react';
import styles from './StaffStatus.module.scss';

interface StaffStatusProps {
  initialStatus?: 'online' | 'busy' | 'away' | 'offline';
  onStatusChange?: (status: 'online' | 'busy' | 'away' | 'offline') => void;
}

export default function StaffStatus({ 
  initialStatus = 'online', 
  onStatusChange 
}: StaffStatusProps) {
  const [status, setStatus] = useState(initialStatus);
  const [isOpen, setIsOpen] = useState(false);
  const [workingHours, setWorkingHours] = useState({
    start: '09:00',
    end: '17:00'
  });

  useEffect(() => {
    // Auto set status based on working hours
    const now = new Date();
    const currentTime = now.getHours() * 100 + now.getMinutes();
    const startTime = parseInt(workingHours.start.replace(':', ''));
    const endTime = parseInt(workingHours.end.replace(':', ''));

    if (currentTime < startTime || currentTime > endTime) {
      if (status === 'online') {
        handleStatusChange('away');
      }
    }
  }, [workingHours, status]);

  const handleStatusChange = (newStatus: 'online' | 'busy' | 'away' | 'offline') => {
    setStatus(newStatus);
    onStatusChange?.(newStatus);
    setIsOpen(false);
  };

  const getStatusConfig = (statusType: string) => {
    const configs = {
      online: {
        color: '#10b981',
        icon: '🟢',
        label: 'Online',
        description: 'Available for new chats'
      },
      busy: {
        color: '#f59e0b',
        icon: '🟡',
        label: 'Busy',
        description: 'Handling urgent matters'
      },
      away: {
        color: '#6b7280',
        icon: '⚫',
        label: 'Away',
        description: 'Temporarily unavailable'
      },
      offline: {
        color: '#ef4444',
        icon: '🔴',
        label: 'Offline',
        description: 'Not available for chats'
      }
    };
    return configs[statusType as keyof typeof configs] || configs.online;
  };

  const currentConfig = getStatusConfig(status);

  return (
    <div className={styles.staffStatus}>
      <button
        className={styles.statusButton}
        onClick={() => setIsOpen(!isOpen)}
        style={{ borderColor: currentConfig.color }}
      >
        <span className={styles.statusIcon}>{currentConfig.icon}</span>
        <span className={styles.statusLabel}>{currentConfig.label}</span>
        <span className={styles.dropdownArrow}>▼</span>
      </button>

      {isOpen && (
        <div className={styles.statusDropdown}>
          <div className={styles.statusHeader}>
            <h4>Set Your Status</h4>
            <button
              className={styles.closeButton}
              onClick={() => setIsOpen(false)}
            >
              ✕
            </button>
          </div>

          <div className={styles.statusOptions}>
            {Object.entries(getStatusConfig('online')).map(([key]) => {
              if (key === 'color') return null;
              const config = getStatusConfig(key);
              return (
                <button
                  key={key}
                  className={`${styles.statusOption} ${status === key ? styles.selected : ''}`}
                  onClick={() => handleStatusChange(key as any)}
                >
                  <span className={styles.optionIcon}>{config.icon}</span>
                  <div className={styles.optionContent}>
                    <span className={styles.optionLabel}>{config.label}</span>
                    <span className={styles.optionDescription}>{config.description}</span>
                  </div>
                  {status === key && (
                    <span className={styles.checkIcon}>✓</span>
                  )}
                </button>
              );
            })}
          </div>

          <div className={styles.workingHours}>
            <h5>Working Hours</h5>
            <div className={styles.timeInputs}>
              <div className={styles.timeInput}>
                <label>Start:</label>
                <input
                  type="time"
                  value={workingHours.start}
                  onChange={(e) => setWorkingHours(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              <div className={styles.timeInput}>
                <label>End:</label>
                <input
                  type="time"
                  value={workingHours.end}
                  onChange={(e) => setWorkingHours(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
            </div>
          </div>

          <div className={styles.statusFooter}>
            <p className={styles.statusNote}>
              Your status will automatically change based on working hours
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
