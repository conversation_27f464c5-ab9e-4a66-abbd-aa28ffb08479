.notificationCenter {
  position: relative;
  display: inline-block;
}

.notificationButton {
  position: relative;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
  
  &:hover {
    background: #f3f4f6;
  }
  
  .bellIcon {
    font-size: 20px;
    display: block;
  }
  
  .notificationBadge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
  }
}

.notificationDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  
  .notificationHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafafa;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #111827;
    }
    
    .headerActions {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .markAllRead {
        font-size: 12px;
        color: #f97316;
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          background: #fef3e2;
        }
      }
      
      .closeButton {
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
        color: #6b7280;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s;
        
        &:hover {
          background: #f3f4f6;
          color: #374151;
        }
      }
    }
  }
  
  .notificationList {
    max-height: 400px;
    overflow-y: auto;
    
    .emptyState {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #9ca3af;
      
      .emptyIcon {
        font-size: 32px;
        margin-bottom: 12px;
        opacity: 0.5;
      }
      
      p {
        margin: 0;
        font-size: 14px;
      }
    }
    
    .notificationItem {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px 20px;
      border-bottom: 1px solid #f9fafb;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background: #f8fafc;
      }
      
      &.unread {
        background: #fef7f0;
        border-left: 3px solid #f97316;
        
        .notificationTitle {
          font-weight: 600;
        }
      }
      
      .notificationIcon {
        font-size: 20px;
        flex-shrink: 0;
        margin-top: 2px;
      }
      
      .notificationContent {
        flex: 1;
        min-width: 0;
        
        .notificationTitle {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #111827;
          margin-bottom: 4px;
          
          .priorityDot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            flex-shrink: 0;
          }
        }
        
        .notificationMessage {
          font-size: 13px;
          color: #6b7280;
          line-height: 1.4;
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        .notificationTime {
          font-size: 11px;
          color: #9ca3af;
        }
      }
      
      .notificationActions {
        flex-shrink: 0;
        
        .deleteButton {
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #9ca3af;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s;
          
          &:hover {
            background: #f3f4f6;
            color: #ef4444;
          }
        }
      }
    }
  }
  
  .notificationFooter {
    padding: 12px 20px;
    border-top: 1px solid #f3f4f6;
    background: #fafafa;
    
    .viewAllButton {
      width: 100%;
      background: none;
      border: none;
      color: #f97316;
      font-size: 13px;
      font-weight: 500;
      padding: 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        background: #fef3e2;
      }
    }
  }
}

// Scrollbar styling
.notificationList::-webkit-scrollbar {
  width: 4px;
}

.notificationList::-webkit-scrollbar-track {
  background: #f9fafb;
}

.notificationList::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
  
  &:hover {
    background: #9ca3af;
  }
}

// Animation
.notificationDropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
