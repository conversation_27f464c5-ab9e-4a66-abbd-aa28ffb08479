'use client';

import { useState } from 'react';
import styles from './ChatMessage.module.scss';

interface ChatMessageProps {
  id: string;
  content: string;
  original_content?: string;
  translated_content?: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated: boolean;
  original_language?: string;
  translated_language?: string;
  translation_confidence?: number;
  show_translation: boolean;
}

export default function ChatMessage({
  id,
  content,
  original_content,
  translated_content,
  sender_type,
  sender_name,
  timestamp,
  is_translated,
  original_language,
  translated_language,
  translation_confidence,
  show_translation
}: ChatMessageProps) {
  const [showOriginal, setShowOriginal] = useState(false);

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const getLanguageFlag = (lang?: string) => {
    const flags: { [key: string]: string } = {
      'en': '🇺🇸',
      'vi': '🇻🇳',
      'ko': '🇰🇷',
      'ja': '🇯🇵',
      'zh': '🇨🇳',
      'es': '🇪🇸',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'th': '🇹🇭',
      'id': '🇮🇩'
    };
    return flags[lang || 'en'] || '🌐';
  };

  return (
    <div className={`${styles.messageContainer} ${styles[sender_type]}`}>
      <div className={styles.messageHeader}>
        <div className={styles.senderInfo}>
          <span className={styles.senderName}>{sender_name}</span>
          {is_translated && (
            <div className={styles.translationInfo}>
              <span className={styles.languageIndicator}>
                {getLanguageFlag(original_language)} → {getLanguageFlag(translated_language)}
              </span>
              {translation_confidence && (
                <span className={styles.confidence}>
                  {Math.round(translation_confidence * 100)}%
                </span>
              )}
            </div>
          )}
        </div>
        <span className={styles.timestamp}>{formatTime(timestamp)}</span>
      </div>

      <div className={styles.messageContent}>
        <div className={styles.messageText}>
          {show_translation && is_translated && !showOriginal 
            ? (translated_content || content)
            : (original_content || content)
          }
        </div>

        {is_translated && show_translation && (
          <div className={styles.translationActions}>
            <button
              className={styles.toggleButton}
              onClick={() => setShowOriginal(!showOriginal)}
            >
              {showOriginal ? 'Show Translation' : 'Show Original'}
            </button>
            
            {showOriginal && original_content && (
              <div className={styles.originalText}>
                <small>Original ({original_language?.toUpperCase()}):</small>
                <p>{original_content}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
