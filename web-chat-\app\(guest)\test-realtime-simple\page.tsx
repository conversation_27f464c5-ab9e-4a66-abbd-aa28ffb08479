'use client'

import { useState, useEffect } from 'react'
import { createClientSupabase } from '../../lib/supabase'

export default function TestRealtimeSimplePage() {
  const [connected, setConnected] = useState(false)
  const [realtimeStatus, setRealtimeStatus] = useState<string>('checking')
  const [testResults, setTestResults] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    testRealtimeConnection()
  }, [])

  const testRealtimeConnection = async () => {
    try {
      setError(null)
      console.log('🔄 Testing Supabase Realtime connection...')

      // Initialize Supabase client
      const supabase = createClientSupabase()
      setConnected(true)

      // Test basic table access
      const tables = [
        'tenant_chat_messages',
        'tenant_chat_sessions', 
        'tenant_chat_session_assignments'
      ]

      const results = []

      for (const table of tables) {
        try {
          const { error } = await supabase
            .from(table)
            .select('id')
            .limit(1)

          results.push({
            table,
            accessible: !error,
            error: error?.message || null
          })

        } catch (err) {
          results.push({
            table,
            accessible: false,
            error: err instanceof Error ? err.message : 'Unknown error'
          })
        }
      }

      setTestResults(results)

      // Test realtime subscription with retry logic
      try {
        const channel = supabase
          .channel('test-channel-' + Date.now()) // Unique channel name
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'tenant_chat_messages'
          }, (payload) => {
            console.log('✅ Realtime test message received:', payload)
          })
          .subscribe((status) => {
            console.log('📡 Realtime subscription status:', status)
            setRealtimeStatus(status)

            if (status === 'SUBSCRIBED') {
              console.log('✅ Realtime is working!')
              // Keep subscription alive longer for testing
              setTimeout(() => {
                setRealtimeStatus('SUBSCRIBED (stable)')
                setTimeout(() => {
                  channel.unsubscribe()
                  setRealtimeStatus('UNSUBSCRIBED (test complete)')
                }, 3000)
              }, 2000)
            } else if (status === 'CLOSED') {
              console.log('⚠️ Realtime connection closed, this is normal after unsubscribe')
              if (realtimeStatus !== 'SUBSCRIBED (stable)') {
                setRealtimeStatus('CLOSED (may retry)')
              }
            } else if (status === 'SUBSCRIPTION_ERROR') {
              console.error('❌ Realtime subscription error')
              setRealtimeStatus('SUBSCRIPTION_ERROR')
              setError('Realtime subscription failed - check your Supabase project settings')
            }
          })

      } catch (err) {
        console.error('❌ Realtime subscription failed:', err)
        setRealtimeStatus('failed')
        setError(err instanceof Error ? err.message : 'Realtime subscription failed')
      }

    } catch (err) {
      console.error('❌ Connection test failed:', err)
      setError(err instanceof Error ? err.message : 'Connection failed')
      setConnected(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUBSCRIBED': return 'text-green-600'
      case 'SUBSCRIPTION_ERROR': 
      case 'CLOSED':
      case 'failed': return 'text-red-600'
      case 'CHANNEL_ERROR': return 'text-orange-600'
      default: return 'text-yellow-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUBSCRIBED': return '✅'
      case 'SUBSCRIPTION_ERROR': 
      case 'CLOSED':
      case 'failed': return '❌'
      case 'CHANNEL_ERROR': return '⚠️'
      default: return '🔄'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Simple Realtime Test</h1>
        
        {/* Connection Status */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className={`p-4 rounded border-2 ${connected ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <div className="flex items-center gap-2">
                <span className="text-lg">{connected ? '✅' : '❌'}</span>
                <span className="font-semibold">Supabase Client</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {connected ? 'Connected successfully' : 'Connection failed'}
              </div>
            </div>

            <div className={`p-4 rounded border-2 ${
              realtimeStatus === 'SUBSCRIBED' ? 'border-green-200 bg-green-50' : 
              realtimeStatus === 'failed' ? 'border-red-200 bg-red-50' : 
              'border-yellow-200 bg-yellow-50'
            }`}>
              <div className="flex items-center gap-2">
                <span className="text-lg">{getStatusIcon(realtimeStatus)}</span>
                <span className="font-semibold">Realtime Status</span>
              </div>
              <div className={`text-sm mt-1 ${getStatusColor(realtimeStatus)}`}>
                {realtimeStatus}
              </div>
            </div>
          </div>
        </div>

        {/* Table Access Test */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Table Access Test</h2>
          
          {testResults.length > 0 ? (
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded border ${
                    result.accessible ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{result.accessible ? '✅' : '❌'}</span>
                      <span className="font-medium">{result.table}</span>
                    </div>
                    <span className={`text-sm ${result.accessible ? 'text-green-600' : 'text-red-600'}`}>
                      {result.accessible ? 'Accessible' : 'Error'}
                    </span>
                  </div>
                  {result.error && (
                    <div className="text-sm text-red-600 mt-1 ml-7">
                      {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-4">
              Running tests...
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Actions */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          
          <div className="flex gap-4">
            <button
              onClick={testRealtimeConnection}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Retest Connection
            </button>
            
            <a
              href="/test-messaging"
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 inline-block"
            >
              Test Messaging
            </a>
            
            <a
              href="/qr/mbblsqjm-i24r2?lang=en"
              className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 inline-block"
            >
              Try Real Chat
            </a>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mt-6">
          <h4 className="font-semibold text-blue-800 mb-2">What this test does:</h4>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• Tests Supabase client connection</li>
            <li>• Checks table accessibility (RLS policies)</li>
            <li>• Tests realtime subscription capability</li>
            <li>• Provides simple pass/fail results</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
