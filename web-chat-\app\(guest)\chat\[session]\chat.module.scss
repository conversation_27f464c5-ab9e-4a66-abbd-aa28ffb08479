/**
 * Enhanced <PERSON><PERSON> Module Styles
 * Supports both original and enhanced chat implementations
 */

.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f8fafc;
  
  p {
    margin-top: 1rem;
    color: #64748b;
    font-size: 1.1rem;
  }
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fef2f2;
  padding: 2rem;
  text-align: center;
  
  h2 {
    color: #dc2626;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }
  
  p {
    color: #7f1d1d;
    margin-bottom: 2rem;
    max-width: 400px;
  }
  
  button {
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
    
    &:hover {
      background: #b91c1c;
    }
  }
}

/* Header */
.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .headerLeft {
    display: flex;
    flex-direction: column;
    
    h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
    }
    
    .sessionInfo {
      font-size: 0.875rem;
      color: #64748b;
      margin-top: 0.25rem;
    }
  }
  
  .headerRight {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
}

/* Connection Status */
.connectionStatus {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: #f1f5f9;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  
  .statusDot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.connected {
      background: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }
    
    &.disconnected {
      background: #ef4444;
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
  }
  
  .statusText {
    color: #475569;
    font-weight: 500;
  }
}

.performanceToggle {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s;
  
  &:hover {
    background: #e2e8f0;
    transform: scale(1.05);
  }
}

/* Performance Panel */
.performancePanel {
  background: #fefce8;
  border-bottom: 1px solid #eab308;
  padding: 1rem 1.5rem;
  
  h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1rem;
    color: #a16207;
    font-weight: 600;
  }
  
  .metricsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
  
  .metric {
    display: flex;
    flex-direction: column;
    
    .metricLabel {
      font-size: 0.75rem;
      color: #a16207;
      font-weight: 500;
      margin-bottom: 0.25rem;
    }
    
    .metricValue {
      font-size: 0.875rem;
      font-weight: 600;
      
      &.excellent { color: #059669; }
      &.good { color: #0891b2; }
      &.poor { color: #ea580c; }
      &.critical { color: #dc2626; }
    }
  }
}

/* Messages Container */
.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: #f8fafc;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

/* Welcome Message */
.welcomeMessage {
  text-align: center;
  padding: 3rem 2rem;
  color: #64748b;
  
  h2 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: #334155;
  }
  
  p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 400px;
    margin: 0 auto;
  }
}

/* Messages */
.message {
  margin-bottom: 1rem;
  display: flex;
  
  &.guest {
    justify-content: flex-end;
    
    .messageContent {
      background: #3b82f6;
      color: white;
      border-radius: 1rem 1rem 0.25rem 1rem;
      max-width: 70%;
    }
  }
  
  &.staff {
    justify-content: flex-start;
    
    .messageContent {
      background: white;
      color: #1e293b;
      border-radius: 1rem 1rem 1rem 0.25rem;
      border: 1px solid #e2e8f0;
      max-width: 70%;
    }
  }
}

.messageContent {
  padding: 0.75rem 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .messageHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    
    .senderName {
      font-size: 0.75rem;
      font-weight: 600;
      opacity: 0.8;
    }
    
    .timestamp {
      font-size: 0.75rem;
      opacity: 0.6;
    }
  }
  
  .messageText {
    margin: 0;
    line-height: 1.5;
    word-wrap: break-word;
  }
  
  .translationInfo {
    margin-top: 0.5rem;
    
    .translationBadge {
      display: inline-block;
      background: rgba(255, 255, 255, 0.2);
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
  }
}

/* Typing Indicator */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border-radius: 1rem 1rem 1rem 0.25rem;
  border: 1px solid #e2e8f0;
  margin-bottom: 1rem;
  max-width: 200px;
  
  .typingDots {
    display: flex;
    gap: 0.25rem;
    
    span {
      width: 6px;
      height: 6px;
      background: #94a3b8;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
    }
  }
  
  .typingText {
    font-size: 0.875rem;
    color: #64748b;
    font-style: italic;
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Message Input */
.messageInputContainer {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem 1.5rem;
  
  .inputWrapper {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  .messageInput {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &:disabled {
      background: #f9fafb;
      color: #9ca3af;
      cursor: not-allowed;
    }
    
    &::placeholder {
      color: #9ca3af;
    }
  }
  
  .sendButton {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    
    &:hover:not(:disabled) {
      background: #2563eb;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
    }
  }
  
  .inputFooter {
    .connectionInfo {
      font-size: 0.75rem;
      color: #64748b;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .latencyInfo {
        color: #94a3b8;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatHeader {
    padding: 0.75rem 1rem;
    
    .headerLeft h1 {
      font-size: 1.25rem;
    }
    
    .headerRight {
      gap: 0.5rem;
    }
  }
  
  .performancePanel {
    padding: 0.75rem 1rem;
    
    .metricsGrid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 0.75rem;
    }
  }
  
  .messagesContainer {
    padding: 0.75rem;
  }
  
  .message {
    &.guest .messageContent,
    &.staff .messageContent {
      max-width: 85%;
    }
  }
  
  .messageInputContainer {
    padding: 0.75rem 1rem;
    
    .inputWrapper {
      gap: 0.5rem;
    }
    
    .messageInput {
      font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .sendButton {
      padding: 0.75rem 1rem;
    }
  }
}

@media (max-width: 480px) {
  .chatHeader {
    .headerLeft h1 {
      font-size: 1.125rem;
    }
    
    .sessionInfo {
      font-size: 0.8rem;
    }
  }
  
  .performancePanel {
    .metricsGrid {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  .welcomeMessage {
    padding: 2rem 1rem;
    
    h2 {
      font-size: 1.25rem;
    }
    
    p {
      font-size: 0.9rem;
    }
  }
}
