.translationToggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.languageFlow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  
  @media (max-width: 640px) {
    display: none;
  }
}

.languageItem {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  
  .flag {
    font-size: 1rem;
  }
  
  .code {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    letter-spacing: 0.05em;
  }
}

.arrow {
  font-size: 1rem;
  color: #6b7280;
  transition: all 0.3s ease;
  
  &.enabled {
    color: #f97316;
    animation: pulse 2s infinite;
  }
  
  &.disabled {
    opacity: 0.3;
  }
}

.toggleContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
  transition: color 0.2s ease;
  
  &.enabled {
    color: #374151;
  }
  
  &.disabled {
    color: #9ca3af;
  }
}

.switch {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.enabled {
    background: linear-gradient(135deg, #f97316, #ea580c);
    box-shadow: 0 2px 4px rgba(249, 115, 22, 0.2);
  }
  
  &.disabled {
    background: #e5e7eb;
  }
}

.thumb {
  display: inline-block;
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  
  &.enabled {
    transform: translateX(1.25rem);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  &.disabled {
    transform: translateX(0.25rem);
  }
}

.status {
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  transition: color 0.2s ease;
  
  &.enabled {
    color: #059669;
  }
  
  &.disabled {
    color: #6b7280;
  }
}

.activeIndicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #059669;
  animation: fadeIn 0.3s ease-out;
  
  .dot {
    width: 0.5rem;
    height: 0.5rem;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
  
  .text {
    font-weight: 500;
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 640px) {
  .translationToggle {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .toggleContainer {
    width: 100%;
    justify-content: space-between;
  }
  
  .activeIndicator {
    font-size: 0.625rem;
    
    .dot {
      width: 0.375rem;
      height: 0.375rem;
    }
  }
}