/**
 * Realtime Performance Monitor
 * Tracks Supabase Realtime performance and connection quality
 */

interface RealtimeMetrics {
  connectionTime: number;
  subscriptionTime: number;
  messageLatency: number[];
  connectionStatus: string;
  lastMessageTime: number;
  errorCount: number;
  reconnectCount: number;
}

interface MessageTiming {
  id: string;
  sentAt: number;
  receivedAt: number;
  latency: number;
  type: 'guest' | 'staff';
}

class RealtimeMonitor {
  private metrics: RealtimeMetrics = {
    connectionTime: 0,
    subscriptionTime: 0,
    messageLatency: [],
    connectionStatus: 'disconnected',
    lastMessageTime: 0,
    errorCount: 0,
    reconnectCount: 0
  };

  private messageTimings: Map<string, MessageTiming> = new Map();
  private connectionStartTime: number = 0;
  private subscriptionStartTime: number = 0;

  // Start monitoring connection
  startConnectionMonitoring(): void {
    this.connectionStartTime = Date.now();
    console.log('🔍 RealtimeMonitor: Starting connection monitoring');
  }

  // Record successful connection
  recordConnection(status: string): void {
    const now = Date.now();
    this.metrics.connectionTime = now - this.connectionStartTime;
    this.metrics.connectionStatus = status;
    
    console.log(`📊 RealtimeMonitor: Connection ${status} in ${this.metrics.connectionTime}ms`);
    
    if (status === 'SUBSCRIBED') {
      this.metrics.subscriptionTime = now - this.subscriptionStartTime;
      console.log(`📊 RealtimeMonitor: Subscription active in ${this.metrics.subscriptionTime}ms`);
    }
  }

  // Start subscription monitoring
  startSubscriptionMonitoring(): void {
    this.subscriptionStartTime = Date.now();
  }

  // Record message sent (for latency calculation)
  recordMessageSent(messageId: string, type: 'guest' | 'staff'): void {
    const timing: MessageTiming = {
      id: messageId,
      sentAt: Date.now(),
      receivedAt: 0,
      latency: 0,
      type
    };
    
    this.messageTimings.set(messageId, timing);
    console.log(`📤 RealtimeMonitor: Message sent ${messageId} at ${timing.sentAt}`);
  }

  // Record message received (calculate latency)
  recordMessageReceived(messageId: string, isRealtime: boolean = true): void {
    const now = Date.now();
    const timing = this.messageTimings.get(messageId);
    
    if (timing) {
      timing.receivedAt = now;
      timing.latency = now - timing.sentAt;
      
      this.metrics.messageLatency.push(timing.latency);
      this.metrics.lastMessageTime = now;
      
      // Keep only last 50 latency measurements
      if (this.metrics.messageLatency.length > 50) {
        this.metrics.messageLatency.shift();
      }
      
      const method = isRealtime ? 'Realtime' : 'Polling';
      console.log(`📥 RealtimeMonitor: Message received ${messageId} via ${method} - Latency: ${timing.latency}ms`);
      
      // Alert if latency is high
      if (timing.latency > 3000) {
        console.warn(`⚠️ RealtimeMonitor: High latency detected: ${timing.latency}ms`);
      }
      
      this.messageTimings.delete(messageId);
    } else {
      console.log(`📥 RealtimeMonitor: Message received ${messageId} (no send record)`);
    }
  }

  // Record error with rate limiting
  recordError(error: string, context: string): void {
    this.metrics.errorCount++;

    // Rate limit error logging to prevent spam
    const now = Date.now();
    const errorKey = `${context}:${error}`;
    const lastLogged = this.lastErrorLog?.get(errorKey) || 0;

    if (now - lastLogged > 5000) { // Only log same error every 5 seconds
      console.error(`❌ RealtimeMonitor: Error in ${context}: ${error}`);
      if (!this.lastErrorLog) this.lastErrorLog = new Map();
      this.lastErrorLog.set(errorKey, now);
    }
  }

  private lastErrorLog?: Map<string, number>;

  // Record reconnection
  recordReconnection(): void {
    this.metrics.reconnectCount++;
    console.log(`🔄 RealtimeMonitor: Reconnection #${this.metrics.reconnectCount}`);
  }

  // Get performance summary
  getPerformanceSummary(): {
    avgLatency: number;
    maxLatency: number;
    minLatency: number;
    connectionQuality: 'excellent' | 'good' | 'poor' | 'critical';
    metrics: RealtimeMetrics;
  } {
    const latencies = this.metrics.messageLatency;
    const avgLatency = latencies.length > 0 
      ? latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length 
      : 0;
    
    const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;
    const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;
    
    let connectionQuality: 'excellent' | 'good' | 'poor' | 'critical';
    if (avgLatency < 1000) connectionQuality = 'excellent';
    else if (avgLatency < 3000) connectionQuality = 'good';
    else if (avgLatency < 5000) connectionQuality = 'poor';
    else connectionQuality = 'critical';
    
    return {
      avgLatency: Math.round(avgLatency),
      maxLatency,
      minLatency,
      connectionQuality,
      metrics: { ...this.metrics }
    };
  }

  // Log performance summary
  logPerformanceSummary(): void {
    const summary = this.getPerformanceSummary();
    
    console.group('📊 RealtimeMonitor: Performance Summary');
    console.log(`Connection Quality: ${summary.connectionQuality.toUpperCase()}`);
    console.log(`Average Latency: ${summary.avgLatency}ms`);
    console.log(`Latency Range: ${summary.minLatency}ms - ${summary.maxLatency}ms`);
    console.log(`Connection Time: ${this.metrics.connectionTime}ms`);
    console.log(`Subscription Time: ${this.metrics.subscriptionTime}ms`);
    console.log(`Error Count: ${this.metrics.errorCount}`);
    console.log(`Reconnect Count: ${this.metrics.reconnectCount}`);
    console.log(`Status: ${this.metrics.connectionStatus}`);
    console.groupEnd();
  }

  // Check for stale messages (sent but not received)
  checkStaleMessages(): void {
    const now = Date.now();
    const staleThreshold = 10000; // 10 seconds
    
    for (const [messageId, timing] of this.messageTimings.entries()) {
      if (now - timing.sentAt > staleThreshold) {
        console.warn(`⚠️ RealtimeMonitor: Stale message detected: ${messageId} (${now - timing.sentAt}ms old)`);
        this.messageTimings.delete(messageId);
      }
    }
  }

  // Reset metrics
  reset(): void {
    this.metrics = {
      connectionTime: 0,
      subscriptionTime: 0,
      messageLatency: [],
      connectionStatus: 'disconnected',
      lastMessageTime: 0,
      errorCount: 0,
      reconnectCount: 0
    };
    this.messageTimings.clear();
    if (this.lastErrorLog) {
      this.lastErrorLog.clear();
    }
    console.log('🔄 RealtimeMonitor: Metrics reset');
  }
}

// Singleton instance
export const realtimeMonitor = new RealtimeMonitor();

// Helper function to generate unique message IDs for tracking
export const generateTrackingId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Helper function to extract message ID from database response
export const extractMessageId = (message: any): string => {
  return message.id || message.message_id || generateTrackingId();
};

export default RealtimeMonitor;
