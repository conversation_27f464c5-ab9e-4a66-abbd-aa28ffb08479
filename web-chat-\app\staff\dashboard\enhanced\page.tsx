'use client';

/**
 * Enhanced Staff Dashboard with Performance Monitoring and Optimization
 * Combines Supabase Realtime with intelligent fallback and monitoring
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { createClientSupabase } from '@/lib/supabase';
import { realtimeMonitor, generateTrackingId, extractMessageId } from '@/lib/realtime-monitor';
import debugUtils from '@/lib/debug-utils';
import styles from '../dashboard.module.scss';

interface ChatSession {
  id: string;
  guest_name: string;
  room_number?: string;
  language: string;
  status: 'active' | 'pending' | 'waiting';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  last_message: string;
  last_message_time: string;
  unread_count: number;
  source: string;
  session_ids?: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  sender_type: 'guest' | 'staff';
  sender_name: string;
  timestamp: string;
  is_translated?: boolean;
  show_translation?: boolean;
  tracking_id?: string;
}

interface User {
  id: string;
  name: string;
  tenant_id: string;
}

export default function EnhancedStaffDashboard() {
  // Core state
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeChatSessions, setActiveChatSessions] = useState<ChatSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');

  // Enhanced state
  const [realtimeConnected, setRealtimeConnected] = useState(false);
  const [usePollingFallback, setUsePollingFallback] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'poor' | 'critical'>('good');

  // Refs
  const router = useRouter();
  const supabaseRef = useRef<any>(null);
  const subscriptionsRef = useRef<any[]>([]);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const performanceIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Utility functions
  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize Supabase with monitoring
  const initializeRealtime = useCallback(() => {
    try {
      realtimeMonitor.startConnectionMonitoring();
      supabaseRef.current = createClientSupabase();
      // Don't set realtimeConnected true here - wait for actual subscription
      realtimeMonitor.recordConnection('CONNECTED');
      console.log('✅ Enhanced Staff Dashboard: Supabase client initialized');
    } catch (err) {
      console.error('❌ Enhanced Staff Dashboard: Failed to initialize Supabase client:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'initialization');
      setRealtimeConnected(false);
    }
  }, []);

  // Enhanced session list refresh with optimistic updates
  const refreshSessionList = useCallback(async () => {
    if (!user) return;

    try {
      const response = await fetch(`/api/chat-sessions?tenant_id=${user.tenant_id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          // Group sessions by guest
          const sessionGroups = new Map<string, any[]>();

          data.sessions.forEach((session: any) => {
            const groupKey = session.qr_info?.room_number || 
                            session.reception_point?.name || 
                            session.qr_info?.location || 
                            'unknown';
            
            if (!sessionGroups.has(groupKey)) {
              sessionGroups.set(groupKey, []);
            }
            sessionGroups.get(groupKey)!.push(session);
          });

          // Transform to UI format with preservation of existing data
          const transformedSessions: ChatSession[] = [];

          for (const [groupKey, sessions] of sessionGroups) {
            const latestSession = sessions.sort((a, b) =>
              new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
            )[0];

            const sessionIds = sessions.map(s => s.id);
            const existingSession = activeChatSessions.find(s => s.id === latestSession.id);

            const transformedSession: ChatSession = {
              id: latestSession.id,
              guest_name: latestSession.qr_info?.room_number ?
                `Room ${latestSession.qr_info.room_number} Guest` :
                'Guest User',
              room_number: latestSession.qr_info?.room_number || undefined,
              language: latestSession.guest_language?.toUpperCase() || 'EN',
              status: latestSession.status as 'active' | 'pending' | 'waiting',
              priority: latestSession.priority as 'low' | 'normal' | 'high' | 'urgent',
              // Preserve existing data or use loading state
              last_message: existingSession?.last_message || 'Loading messages...',
              last_message_time: existingSession?.last_message_time || formatTimeAgo(latestSession.updated_at),
              unread_count: existingSession?.unread_count || 0,
              source: latestSession.qr_info?.location || latestSession.reception_point?.name || 'Direct',
              session_ids: sessionIds
            };

            transformedSessions.push(transformedSession);
          }

          setActiveChatSessions(transformedSessions);
          console.log('✅ Enhanced Staff Dashboard: Session list refreshed');

          // Load message counts for all sessions (enhanced version)
          loadMessageCountsEnhanced(transformedSessions);
        }
      }
    } catch (error) {
      console.error('❌ Enhanced Staff Dashboard: Error refreshing session list:', error);
      realtimeMonitor.recordError(error instanceof Error ? error.message : 'Unknown error', 'refreshSessionList');
    }
  }, [user, activeChatSessions]);

  // Enhanced message counts loading with better performance
  const loadMessageCountsEnhanced = useCallback(async (sessions: ChatSession[]) => {
    console.log('🔍 Enhanced Staff Dashboard: Loading message counts for', sessions.length, 'sessions');
    
    // Process sessions in batches to avoid overwhelming the API
    const batchSize = 5;
    for (let i = 0; i < sessions.length; i += batchSize) {
      const batch = sessions.slice(i, i + batchSize);
      
      await Promise.all(batch.map(async (session) => {
        try {
          const sessionIds = session.session_ids && session.session_ids.length > 0 
            ? session.session_ids 
            : [session.id];
          
          // Load messages from all sessions for this guest
          const allMessages: any[] = [];
          
          for (const sessionId of sessionIds) {
            const response = await fetch(`/api/messages?session_id=${sessionId}&limit=10&offset=0`);
            if (response.ok) {
              const data = await response.json();
              if (data.success && data.messages) {
                allMessages.push(...data.messages);
              }
            }
          }
          
          if (allMessages.length > 0) {
            // Sort messages by timestamp to get the latest
            allMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            const lastMessage = allMessages[allMessages.length - 1];
            
            // Count unread messages (guest messages only, exclude if currently selected)
            const unreadCount = session.id === selectedSession 
              ? 0 
              : allMessages.filter((msg: any) => msg.sender_type === 'guest').length;
            
            // Update session with real last message and unread count
            setActiveChatSessions(prev => prev.map(s => 
              s.id === session.id ? {
                ...s,
                last_message: lastMessage.content.substring(0, 50) + (lastMessage.content.length > 50 ? '...' : ''),
                last_message_time: formatTimeAgo(lastMessage.created_at),
                unread_count: unreadCount
              } : s
            ));
          } else {
            // No messages found
            setActiveChatSessions(prev => prev.map(s => 
              s.id === session.id ? {
                ...s,
                last_message: 'No messages yet',
                last_message_time: formatTimeAgo(new Date().toISOString()),
                unread_count: 0
              } : s
            ));
          }
        } catch (error) {
          console.error(`❌ Enhanced Staff Dashboard: Error loading messages for session ${session.id}:`, error);
          realtimeMonitor.recordError(error instanceof Error ? error.message : 'Unknown error', `loadMessages-${session.id}`);
        }
      }));
      
      // Small delay between batches to prevent API overload
      if (i + batchSize < sessions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }, [selectedSession]);

  // Enhanced realtime subscriptions
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!supabaseRef.current || !user) return;

    console.log('🔄 Enhanced Staff Dashboard: Setting up realtime subscriptions');
    realtimeMonitor.startSubscriptionMonitoring();

    try {
      // Messages subscription with enhanced tracking
      const messagesChannel = supabaseRef.current
        .channel('enhanced-staff-messages')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'tenant_chat_messages',
            filter: `tenant_id=eq.${user.tenant_id}`
          },
          (payload: any) => {
            const messageId = extractMessageId(payload.new);
            console.log('🔔 Enhanced Staff Dashboard: New message received via realtime:', {
              id: messageId,
              session_id: payload.new.chat_session_id,
              sender_type: payload.new.sender_type,
              content: payload.new.content?.substring(0, 50),
              tenant_id: payload.new.tenant_id
            });

            // Record message received for performance tracking
            realtimeMonitor.recordMessageReceived(messageId, true);

            // Update messages if this is for the selected session
            if (selectedSession && payload.new.chat_session_id === selectedSession) {
              const newMessage: ChatMessage = {
                id: payload.new.id,
                content: payload.new.content,
                sender_type: payload.new.sender_type,
                sender_name: payload.new.sender_name || (payload.new.sender_type === 'guest' ? 'Guest' : 'Staff'),
                timestamp: payload.new.created_at,
                is_translated: payload.new.is_translated || false,
                show_translation: false,
                tracking_id: messageId
              };

              setMessages(prev => {
                const exists = prev.some(msg => msg.id === newMessage.id);
                if (exists) return prev;
                console.log('➕ Enhanced Staff Dashboard: Adding new message to chat:', newMessage.id);
                return [...prev, newMessage];
              });

              setTimeout(scrollToBottom, 100);
            }

            // Update session list immediately with optimistic updates
            const sessionId = payload.new.chat_session_id;
            setActiveChatSessions(prev => {
              return prev.map(session => {
                const belongsToSession = session.id === sessionId || 
                  (session.session_ids && session.session_ids.includes(sessionId));
                
                if (belongsToSession) {
                  console.log('🔄 Enhanced Staff Dashboard: Updating session:', session.id, 'with new message from:', payload.new.sender_type);
                  return {
                    ...session,
                    last_message: payload.new.content.substring(0, 50) + (payload.new.content.length > 50 ? '...' : ''),
                    last_message_time: formatTimeAgo(payload.new.created_at),
                    unread_count: payload.new.sender_type === 'guest' && session.id !== selectedSession 
                      ? session.unread_count + 1 
                      : session.unread_count
                  };
                }
                return session;
              });
            });
          }
        )
        .subscribe((status: string) => {
          console.log('📡 Enhanced Staff Dashboard: Messages subscription status:', status);
          realtimeMonitor.recordConnection(status);
          
          if (status === 'SUBSCRIBED') {
            console.log('✅ Enhanced Staff Dashboard: Realtime messages subscription active');
            setRealtimeConnected(true);
            setUsePollingFallback(false);
          } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            console.error('❌ Enhanced Staff Dashboard: Messages subscription error');
            realtimeMonitor.recordError(`Subscription ${status}`, 'messages-subscription');
            setRealtimeConnected(false);
            setUsePollingFallback(true);
          }
        });

      // Store subscriptions for cleanup
      subscriptionsRef.current = [messagesChannel];

    } catch (err) {
      console.error('❌ Enhanced Staff Dashboard: Failed to setup realtime subscriptions:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'setupRealtimeSubscriptions');
      setUsePollingFallback(true);
    }
  }, [user, selectedSession]);

  // Cleanup subscriptions
  const cleanupSubscriptions = useCallback(() => {
    console.log('🧹 Enhanced Staff Dashboard: Cleaning up realtime subscriptions');
    subscriptionsRef.current.forEach(subscription => {
      try {
        subscription.unsubscribe();
      } catch (err) {
        console.warn('Warning: Failed to unsubscribe:', err);
      }
    });
    subscriptionsRef.current = [];
    setRealtimeConnected(false);
  }, []);

  // Enhanced polling fallback
  const startPollingFallback = useCallback(() => {
    if (!usePollingFallback || pollingIntervalRef.current) return;

    console.log('🔄 Enhanced Staff Dashboard: Starting enhanced polling fallback');

    let currentInterval = 1500; // Start with 1.5 seconds (much faster!)
    let noActivityCount = 0;

    const poll = async () => {
      if (!document.hidden && user) {
        try {
          await refreshSessionList();

          // Adaptive interval based on activity
          noActivityCount++;
          if (noActivityCount > 3) {
            currentInterval = Math.min(currentInterval * 1.1, 5000); // Max 5 seconds, slower growth
          } else {
            // Reset to fast polling if there's activity
            currentInterval = 1500;
            noActivityCount = 0;
          }
        } catch (err) {
          console.error('Enhanced Staff Dashboard: Polling error:', err);
          realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'polling');
        }
      }

      if (usePollingFallback) {
        pollingIntervalRef.current = setTimeout(poll, currentInterval);
      }
    };

    poll();
  }, [usePollingFallback, user, refreshSessionList]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearTimeout(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log('⏹️ Enhanced Staff Dashboard: Polling stopped');
    }
  }, []);

  // Send message with tracking
  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!selectedSession || !content.trim()) return false;

    const trackingId = generateTrackingId();
    realtimeMonitor.recordMessageSent(trackingId, 'staff');

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: selectedSession,
          sender_type: 'staff',
          sender_name: user?.name || 'Staff',
          content: content.trim(),
          tracking_id: trackingId
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          console.log('✅ Enhanced Staff Dashboard: Message sent successfully:', trackingId);

          // Optimistic UI update
          const newMsg: ChatMessage = {
            id: data.message.id,
            content: content.trim(),
            sender_type: 'staff',
            sender_name: user?.name || 'Staff',
            timestamp: data.message.created_at,
            tracking_id: trackingId
          };

          setMessages(prev => [...prev, newMsg]);
          setTimeout(scrollToBottom, 100);

          return true;
        }
      }

      throw new Error('Failed to send message');
    } catch (err) {
      console.error('❌ Enhanced Staff Dashboard: Failed to send message:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'sendMessage');
      return false;
    }
  }, [selectedSession, user]);

  // Load messages for selected session
  const loadMessages = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/messages?session_id=${sessionId}&limit=50`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.messages)) {
          const messagesWithTracking = data.messages.map((msg: any) => ({
            ...msg,
            tracking_id: extractMessageId(msg)
          }));
          setMessages(messagesWithTracking);
          setTimeout(scrollToBottom, 100);
        }
      }
    } catch (err) {
      console.error('❌ Enhanced Staff Dashboard: Failed to load messages:', err);
      realtimeMonitor.recordError(err instanceof Error ? err.message : 'Unknown error', 'loadMessages');
    }
  }, []);

  // Handle session selection
  const handleSessionSelect = useCallback((sessionId: string) => {
    setSelectedSession(sessionId);

    // Mark session as read
    setActiveChatSessions(prev => prev.map(session =>
      session.id === sessionId ? { ...session, unread_count: 0 } : session
    ));

    loadMessages(sessionId);
  }, [loadMessages]);

  // Initialize dashboard
  useEffect(() => {
    // Run debug check
    debugUtils.runQuickDebug();

    // Check authentication
    const token = localStorage.getItem('staff_token');
    const userData = localStorage.getItem('staff_user');

    if (!token || !userData) {
      router.push('/staff');
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);
      initializeRealtime();

      console.log('🚀 Enhanced Staff Dashboard: Initialized with user:', parsedUser.name);
    } catch (error) {
      console.error('Error parsing user data:', error);
      router.push('/staff');
    } finally {
      setLoading(false);
    }
  }, [router, initializeRealtime]);

  // Setup realtime when user is loaded
  useEffect(() => {
    if (user && supabaseRef.current) {
      console.log('🔄 Enhanced Staff Dashboard: Setting up realtime for user:', user.id);
      refreshSessionList();
      setupRealtimeSubscriptions();

      return () => {
        cleanupSubscriptions();
      };
    }
  }, [user, refreshSessionList, setupRealtimeSubscriptions, cleanupSubscriptions]);

  // Start polling fallback when needed
  useEffect(() => {
    if (usePollingFallback) {
      startPollingFallback();
    } else {
      stopPolling();
    }
  }, [usePollingFallback, startPollingFallback, stopPolling]);

  // Performance monitoring
  useEffect(() => {
    performanceIntervalRef.current = setInterval(() => {
      const metrics = realtimeMonitor.getPerformanceSummary();
      setPerformanceMetrics(metrics);
      setConnectionQuality(metrics.connectionQuality);

      // Log performance every 30 seconds
      realtimeMonitor.logPerformanceSummary();
    }, 30000);

    return () => {
      if (performanceIntervalRef.current) {
        clearInterval(performanceIntervalRef.current);
      }
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
      cleanupSubscriptions();
      if (performanceIntervalRef.current) {
        clearInterval(performanceIntervalRef.current);
      }
    };
  }, [stopPolling, cleanupSubscriptions]);

  if (loading) {
    return <div className={styles.loading}>Loading Enhanced Dashboard...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Enhanced Staff Dashboard</h1>
        <div className={styles.connectionStatus}>
          <span className={`${styles.statusIndicator} ${realtimeConnected ? styles.connected : styles.disconnected}`}>
            {realtimeConnected ? '🟢 Realtime' : usePollingFallback ? '🟡 Polling' : '🔴 Disconnected'}
          </span>
          <span className={styles.qualityIndicator}>
            Quality: {connectionQuality.toUpperCase()}
          </span>
        </div>
      </div>
      
      {/* Performance Metrics Panel */}
      {performanceMetrics && (
        <div className={styles.performancePanel}>
          <h3>Performance Metrics</h3>
          <div className={styles.metrics}>
            <span>Avg Latency: {performanceMetrics.avgLatency}ms</span>
            <span>Connection: {performanceMetrics.metrics.connectionTime}ms</span>
            <span>Errors: {performanceMetrics.metrics.errorCount}</span>
          </div>
        </div>
      )}

      <div className={styles.content}>
        {/* Session List */}
        <div className={styles.sidebar}>
          <div className={styles.sessionList}>
            {activeChatSessions.map((session) => (
              <div
                key={session.id}
                className={`${styles.sessionItem} ${selectedSession === session.id ? styles.selected : ''}`}
                onClick={() => handleSessionSelect(session.id)}
              >
                <div className={styles.sessionHeader}>
                  <span className={styles.guestName}>{session.guest_name}</span>
                  <span className={styles.language}>{session.language}</span>
                </div>
                <div className={styles.lastMessage}>{session.last_message}</div>
                <div className={styles.sessionMeta}>
                  <span className={styles.time}>{session.last_message_time}</span>
                  {session.unread_count > 0 && (
                    <span className={styles.unreadBadge}>{session.unread_count}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className={styles.chatArea}>
          {selectedSession ? (
            <>
              <div className={styles.messagesContainer}>
                {messages.map((message) => (
                  <div key={message.id} className={`${styles.message} ${styles[message.sender_type]}`}>
                    <div className={styles.messageContent}>
                      <span className={styles.senderName}>{message.sender_name}</span>
                      <p>{message.content}</p>
                      <span className={styles.timestamp}>{formatTimeAgo(message.timestamp)}</span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
              
              <div className={styles.messageInput}>
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  onKeyPress={async (e) => {
                    if (e.key === 'Enter' && newMessage.trim()) {
                      const success = await sendMessage(newMessage);
                      if (success) {
                        setNewMessage('');
                      }
                    }
                  }}
                />
                <button onClick={async () => {
                  if (newMessage.trim()) {
                    const success = await sendMessage(newMessage);
                    if (success) {
                      setNewMessage('');
                    }
                  }
                }}>
                  Send
                </button>
              </div>
            </>
          ) : (
            <div className={styles.noSelection}>
              <p>Select a chat session to start messaging</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
