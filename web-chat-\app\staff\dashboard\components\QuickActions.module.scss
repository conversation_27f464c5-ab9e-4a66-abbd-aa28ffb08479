.quickActions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
}

.toggleButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f97316;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  transition: all 0.2s;

  &:hover {
    background: #ea580c;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);
  }

  .toggleIcon {
    font-size: 18px;
  }

  .toggleText {
    font-size: 14px;
  }

  .arrow {
    font-size: 10px;
    transition: transform 0.2s;

    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.actionsPanel {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 12px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  animation: slideUp 0.2s ease-out;

  .actionsGrid {
    padding: 8px;
    min-width: 280px;

    .actionItem {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
      padding: 12px;
      background: none;
      border: none;
      border-left: 3px solid transparent;
      cursor: pointer;
      transition: all 0.2s;
      text-align: left;
      border-radius: 6px;
      margin-bottom: 4px;

      &:hover {
        background: #f9fafb;
        border-left-color: currentColor;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .actionIcon {
        font-size: 20px;
        flex-shrink: 0;
      }

      .actionContent {
        display: flex;
        flex-direction: column;
        min-width: 0;

        .actionLabel {
          font-size: 14px;
          font-weight: 500;
          color: #111827;
          margin-bottom: 2px;
        }

        .actionDescription {
          font-size: 12px;
          color: #6b7280;
          line-height: 1.3;
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .quickActions {
    bottom: 80px;
    right: 16px;
  }

  .actionsPanel {
    right: 0;
    left: -200px;

    .actionsGrid {
      min-width: 260px;
    }
  }
}
